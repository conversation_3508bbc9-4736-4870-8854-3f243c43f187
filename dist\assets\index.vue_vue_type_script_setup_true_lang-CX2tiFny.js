import{_ as N,a as U}from"./preview-close-one-CPPPRLOZ.js";import{_ as C}from"./lock-CFXZdX3i.js";import{d as E,fk as I,z as S,y as T,r as i,o as j,fl as m,s as L,b as P,f as e,w as t,j as d,t as f,fm as h,i as s,fn as q,fe as D,G,e as H,l as F,B as J}from"./index-DSmp6iCg.js";import{a as O}from"./headers-DsTJCVw-.js";import{_ as Q,a as W}from"./FormItem-Be7I1W2B.js";import{_ as X}from"./Checkbox-CuJqEfdi.js";import{_ as Y}from"./Input-D61U907N.js";const Z={class:"flex-y-center justify-between"},ie=E({__name:"index",emits:["update:modelValue"],setup(ee,{emit:ne}){const k=I(),b=S(),{t:y}=T(),A=i({pwd:{required:!0,trigger:"input",validator:(o,n)=>n?n.length<6?new Error("รหัสผ่านต้องยาวอย่างน้อย 6 ตัวอักษร"):!0:new Error(y("login.passwordRuleTip"))}}),a=i({account:null,pwd:null}),l=i(!1),_=i(!1),g=i(null);function p(){g.value?.validate(async o=>{if(o)return;_.value=!0;const{account:n,pwd:u}=a.value;l.value?m.set("loginAccount",{account:n,pwd:u}):m.remove("loginAccount");const r=await k.login(n,u);_.value=!1,!r.success&&r.message&&b.error(r.message)})}j(()=>{x()});function x(){const o=m.get("loginAccount");o&&(a.value=o,l.value=!0)}return(o,n)=>{const u=O,r=D,w=Y,v=W,B=C,R=U,V=N,$=X,z=J,K=G,M=Q;return P(),L("div",null,[e(u,{depth:"3",class:"text-center"},{default:t(()=>[d(f(o.$t("login.signInTitle")),1)]),_:1}),e(M,{ref_key:"formRef",ref:g,rules:s(A),model:s(a),"show-label":!1,size:"large",onKeydown:h(q(p,["prevent"]),["enter"])},{default:t(()=>[e(v,{path:"account"},{default:t(()=>[e(w,{value:s(a).account,"onUpdate:value":n[0]||(n[0]=c=>s(a).account=c),clearable:"",placeholder:o.$t("login.accountPlaceholder")},{prefix:t(()=>[e(r,{class:"mr-2"})]),_:1},8,["value","placeholder"])]),_:1}),e(v,{path:"pwd"},{default:t(()=>[e(w,{value:s(a).pwd,"onUpdate:value":n[1]||(n[1]=c=>s(a).pwd=c),type:"password",placeholder:o.$t("login.passwordPlaceholder"),clearable:"","show-password-on":"click"},{prefix:t(()=>[e(B,{class:"mr-2"})]),"password-invisible-icon":t(()=>[e(R)]),"password-visible-icon":t(()=>[e(V)]),_:1},8,["value","placeholder"])]),_:1}),e(K,{vertical:"",size:20},{default:t(()=>[H("div",Z,[e($,{checked:s(l),"onUpdate:checked":n[2]||(n[2]=c=>F(l)?l.value=c:null)},{default:t(()=>[d(f(o.$t("login.rememberMe")),1)]),_:1},8,["checked"])]),e(z,{block:"",type:"primary",size:"large",loading:s(_),disabled:s(_),onClick:p,onKeydown:h(p,["enter"])},{default:t(()=>[d(f(o.$t("login.signIn")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["rules","model","onKeydown"])])}}});export{ie as _};

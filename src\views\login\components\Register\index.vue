<script setup lang="ts">
import http from "@/service/axios";
import type { FormInst } from "naive-ui";
import { useAuthStore } from "@/store";
const authStore = useAuthStore();
const formRef = ref<FormInst | null>(null);
const message = useMessage();
const loading = ref(false);
const emit = defineEmits(["update:modelValue"]);
function toLogin() {
  emit("update:modelValue", "login");
}
const { t } = useI18n();

const rules = ref<any>({
  phone: {
    required: true,
    trigger: "input",
    validator: (rule: any, value: string) => {
      if (!value) return new Error(t("login.telRuleTip"));

      if (!/^\d+$/.test(value)) {
        return new Error("เบอร์โทรต้องเป็นตัวเลขเท่านั้น");
      }

      const phoneRegex = /^[0-9]{10}$/;
      if (!phoneRegex.test(value)) {
        return new Error("เบอร์โทรต้องยาว 10 ตัวเท่านั้น");
      }

      return true;
    },
  },
  pwd: {
    required: true,
    trigger: "input",
    validator: (rule: any, value: string) => {
      if (!value) return new Error(t("login.passwordRuleTip"));
      if (value.length < 6) {
        return new Error("รหัสผ่านต้องยาวอย่างน้อย 6 ตัวอักษร");
      }
      return true;
    },
  },
  rePwd: {
    required: true,
    trigger: "input",
    validator: (rule: any, value: string) => {
      if (!value) return new Error(t("login.checkPasswordRuleTip"));
      if (value !== formValue.value.pwd) {
        return new Error("รหัสผ่านไม่ตรงกัน");
      }
      return true;
    },
  },
  rank: {
    type: "number",
    required: true,
    trigger: ["input", "change"],
    message: "กรุณาเลือกยศ",
  },
  firstName: {
    required: true,
    trigger: "input",
    message: "กรุณากรอกชื่อ",
  },
  lastName: {
    required: true,
    trigger: "input",
    message: "กรุณากรอกนามสกุล",
  },
  position: {
    type: "number",
    required: true,
    trigger: ["input", "change"],
    message: "กรุณาเลือกตำแหน่ง",
  },
  region: {
    type: "number",
    required: true,
    trigger: ["input", "change"],
    message: "กรุณาเลือกภาค",
  },
  province: {
    type: "number",
    required: true,
    trigger: ["input", "change"],
    message: "กรุณาเลือกจังหวัด",
  },
  department: {
    type: "number",
    required: true,
    trigger: ["input", "change"],
    message: "กรุณาเลือกสังกัด",
  },
});

const formValue = ref({
  phone: null,
  pwd: null,
  rePwd: null,
  rank: null,
  firstName: null,
  lastName: null,
  position: null,
  region: null,
  province: null,
  department: null,
});

const rankOptions = ref([]);

const positionOptions = ref([]);

const regionOptions = ref([]);

const provincesByRegion = ref([]);

const departmentsByProvince = ref([]);

const provinceOptions = ref([]);
const departmentOptions = ref([]);

const isRead = ref(false);

onMounted(() => {
  getRanks();
  getPosition();
  getRegion();
});
const getRanks = () => {
  http.get("GetRanks").then((response) => {
    rankOptions.value = response.data;
  });
};
const getPosition = () => {
  http.get("GetPositionType").then((response) => {
    positionOptions.value = response.data;
  });
};
const getRegion = () => {
  http.get("GetRegion").then((response) => {
    regionOptions.value = response.data;
  });
};
const selectRegion = () => {
  formValue.value.province = null;
  formValue.value.department = null;
  provinceOptions.value = [];
  departmentOptions.value = [];
  if (formValue.value.region) {
    const params = {
      region_id: formValue.value.region,
    };
    http.get("getSelectRegion", { params }).then((response) => {
      provinceOptions.value = response.data;
    });
  } else {
    provinceOptions.value = [];
    departmentOptions.value = [];
    formValue.value.province = null;
    formValue.value.department = null;
  }
};
const selectProvince = () => {
  formValue.value.department = null;
  departmentOptions.value = [];
  if (formValue.value.province) {
    const params = {
      region_id: formValue.value.region,
      province_id: formValue.value.province,
    };
    http.get("GetUnits", { params }).then((response) => {
      departmentOptions.value = response.data;
    });
  } else {
    departmentOptions.value = [];
    formValue.value.department = null;
  }
};
function handleRegister() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      loading.value = true;
      const obj = {
        tel: formValue.value.phone,
        password: formValue.value.pwd,
        rank_id: formValue.value.rank,
        position_type: formValue.value.position,
        first_name: formValue.value.firstName,
        last_name: formValue.value.lastName,
        region: formValue.value.region,
        province: formValue.value.province,
        unit: formValue.value.department,
      };
      http
        .post("auth/register", obj)
        .then((response) => {
          if (response.data.success) {
            message.success("สมัครสมาชิกสำเร็จ");
            authStore.login(formValue.value.phone, formValue.value.pwd);
            loading.value = false;
          }
        })
        .catch((err) => {
          message.error(err.response.data.mes);
          loading.value = false;
        });

      // message.error("เกิดข้อผิดพลาดในการสมัครสมาชิก");

      // message.success("สมัครสมาชิกสำเร็จ");
    } else {
      message.error("กรุณากรอกข้อมูลให้ครบถ้วน");
    }
  });
}
</script>

<template>
  <div>
    <n-h2 depth="3" class="text-center mb-6">
      {{ $t("login.registerTitle") }}
    </n-h2>
    <n-form ref="formRef" :rules="rules" :model="formValue" :show-label="false">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <n-card title="ข้อมูลการเข้าสู่ระบบ" class="mb-3">
          <n-grid cols="1 700:3 " :x-gap="12">
            <n-gi>
              <n-form-item path="phone">
                <n-input
                  v-model:value="formValue.phone"
                  clearable
                  placeholder="กรอกเบอร์โทรศัพท์"
                />
              </n-form-item>
            </n-gi>

            <n-gi>
              <n-form-item path="pwd">
                <n-input
                  v-model:value="formValue.pwd"
                  type="password"
                  :placeholder="$t('login.passwordPlaceholder')"
                  clearable
                  show-password-on="click"
                >
                  <template #password-invisible-icon>
                    <icon-park-outline-preview-close-one />
                  </template>
                  <template #password-visible-icon>
                    <icon-park-outline-preview-open />
                  </template>
                </n-input>
              </n-form-item>
            </n-gi>

            <n-gi>
              <n-form-item path="rePwd">
                <n-input
                  v-model:value="formValue.rePwd"
                  type="password"
                  :placeholder="$t('login.checkPasswordPlaceholder')"
                  clearable
                  show-password-on="click"
                >
                  <template #password-invisible-icon>
                    <icon-park-outline-preview-close-one />
                  </template>
                  <template #password-visible-icon>
                    <icon-park-outline-preview-open />
                  </template>
                </n-input>
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-card>

        <!-- ข้อมูลส่วนตัว -->
        <n-card title="ข้อมูลส่วนตัว" class="mb-3">
          <n-grid cols="1 700:2" :x-gap="12">
            <n-gi>
              <n-form-item path="rank">
                <n-select
                  v-model:value="formValue.rank"
                  :options="rankOptions"
                  placeholder="เลือกยศ"
                  label-field="rank_name"
                  value-field="rank_id"
                  clearable
                  filterable
                />
              </n-form-item>
            </n-gi>

            <n-gi>
              <n-form-item path="position">
                <n-select
                  v-model:value="formValue.position"
                  :options="positionOptions"
                  placeholder="เลือกตำแหน่ง"
                  label-field="position_type"
                  value-field="position_id"
                  clearable
                  filterable
                />
              </n-form-item>
            </n-gi>

            <n-gi>
              <n-form-item path="firstName">
                <n-input
                  v-model:value="formValue.firstName"
                  clearable
                  placeholder="กรอกชื่อ"
                />
              </n-form-item>
            </n-gi>

            <n-gi>
              <n-form-item path="lastName">
                <n-input
                  v-model:value="formValue.lastName"
                  clearable
                  placeholder="กรอกนามสกุล"
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-card>

        <!-- ข้อมูลสังกัด -->
        <n-card title="ข้อมูลสังกัด" class="mb-3">
          <n-grid cols="1 700:3" :x-gap="12">
            <n-gi>
              <n-form-item path="region">
                <n-select
                  v-model:value="formValue.region"
                  :options="regionOptions"
                  placeholder="เลือกภาค"
                  label-field="region_name"
                  value-field="region_id"
                  clearable
                  filterable
                  @update:value="selectRegion"
                />
              </n-form-item>
            </n-gi>

            <n-gi>
              <n-form-item path="province">
                <n-select
                  v-model:value="formValue.province"
                  :options="provinceOptions"
                  placeholder="เลือกจังหวัด"
                  clearable
                  filterable
                  label-field="province_name"
                  value-field="province_id"
                  :disabled="!formValue.region"
                  @update:value="selectProvince"
                />
              </n-form-item>
            </n-gi>

            <n-gi>
              <n-form-item path="department">
                <n-select
                  v-model:value="formValue.department"
                  :options="departmentOptions"
                  placeholder="เลือกสังกัด"
                  clearable
                  filterable
                  label-field="unit_name"
                  value-field="unit_id"
                  :disabled="!formValue.province || departmentOptions.length === 0"
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </n-card>
      </div>

      <n-form-item>
        <n-space vertical :size="20" class="w-full">
          <n-checkbox v-model:checked="isRead">
            {{ $t("login.readAndAgree") }}
            <n-button type="primary" text>
              {{ $t("login.userAgreement") }}
            </n-button>
          </n-checkbox>
          <n-button
            block
            type="primary"
            @click="handleRegister"
            :disabled="!isRead"
            :loading="loading"
          >
            {{ $t("login.signUp") }}
          </n-button>
          <n-flex justify="center">
            <n-text>{{ $t("login.haveAccountText") }}</n-text>
            <n-button text type="primary" @click="toLogin">
              {{ $t("login.signIn") }}
            </n-button>
          </n-flex>
        </n-space>
      </n-form-item>
    </n-form>
  </div>
</template>

<style scoped></style>

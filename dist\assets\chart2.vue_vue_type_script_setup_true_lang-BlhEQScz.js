import{d as C,u as E,D as i,y as B,r as y,o as _,a9 as w,a as $,I as u,H as k,s as L,b as N,e as H,aa as T}from"./index-DSmp6iCg.js";import"./index-D3Jd_Dd9.js";import{h as I}from"./moment-zH0z38ay.js";const A={class:"-mt-5"},F=C({__name:"chart2",props:["year"],setup(b){const v=b,d=E(),n=i(()=>d.primaryColor),{t:c,locale:s}=B();function p(t,e=1){const a=t.replace("#","");if(a.length!==6)return t;const o=parseInt(a,16),m=o>>16&255,S=o>>8&255,z=o&255;return`rgba(${m}, ${S}, ${z}, ${e})`}const g=i(()=>p(n.value,.2));i(()=>p(n.value,.05));let r=null;const h=y(null),x=i(()=>["chart.months.jan","chart.months.feb","chart.months.mar","chart.months.apr","chart.months.may","chart.months.jun","chart.months.jul","chart.months.aug","chart.months.sep","chart.months.oct","chart.months.nov","chart.months.dec"].map(e=>c(e))),l=()=>{if(r){const t={title:{left:"center",textStyle:{color:n.value,fontSize:18},subtextStyle:{color:"#6B7280",fontSize:12}},tooltip:{axisPointer:{type:"shadow",label:{backgroundColor:"#6a7985"}},trigger:"axis",backgroundColor:"rgba(255, 255, 255, 0.9)",borderRadius:10,padding:[10,14],borderColor:"#E5E7EB",borderWidth:1,textStyle:{color:"#374151",fontSize:13},formatter:e=>{const a=e[0],o=new Intl.NumberFormat(s.value==="thTH"?"th-TH":s.value==="zhCN"?"zh-CN":"en-US").format(a.value);return`<div style="font-weight:600;margin-bottom:6px;font-size:14px">${a.axisValue}</div>
                  <div style="color:${n.value};">${a.marker} ${c("chart.totalAmount")} : ${o}</div>`}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:x.value,axisTick:{alignWithLabel:!0,lineStyle:{color:"#E5E7EB"}},axisLine:{lineStyle:{color:"#E5E7EB"}},axisLabel:{fontSize:13,color:"#6B7280",padding:[8,0]}},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}},axisLabel:{color:"#6B7280",fontSize:13,padding:[0,8],formatter:e=>new Intl.NumberFormat(s.value==="thTH"?"th-TH":s.value==="zhCN"?"zh-CN":"en-US",{notation:"compact",compactDisplay:"short"}).format(e)}},series:[{type:"bar",barWidth:"60%",itemStyle:{color:new T(0,0,0,1,[{offset:0,color:n.value},{offset:1,color:g.value}])},data:f.value}],legend:{data:[c("chart.reports")],bottom:"0",textStyle:{color:"#6B7280"}}};r.setOption(t)}},f=y([]);return _(()=>{h.value&&(r=w(h.value),l()),window.addEventListener("resize",()=>{r&&r.resize()})}),$(()=>{r&&r.dispose()}),u(()=>d.primaryColor,l),u(()=>s.value,l),u(()=>v.year,t=>{if(t!=null){const a={year:I(t).format("YYYY")};k.get("v1/dashboard/getGraphV1",{params:a}).then(o=>{f.value=o.data.map(m=>Number(m.totalTHB).toFixed(2)),l()}).catch(o=>{console.error("API Error:",o)})}},{immediate:!0}),(t,e)=>(N(),L("div",A,[H("div",{ref_key:"barRef",ref:h,class:"h-400px w-full"},null,512)]))}});export{F as _};

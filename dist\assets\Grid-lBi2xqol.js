import{gQ as q,D as g,r as N,gD as F,gR as V,g1 as U,d as O,a0 as E,g2 as z,gS as H,gH as x,gx as k,gI as Y,a2 as J,fP as B,o as W,gl as Z,fN as j,gT as K,fw as ee,fy as ne,gU as P,gM as te}from"./index-DSmp6iCg.js";function se(e){if(typeof e=="number")return{"":e.toString()};const n={};return e.split(/ +/).forEach(r=>{if(r==="")return;const[s,t]=r.split(":");t===void 0?n[""]=s:n[s]=t}),n}function _(e,n){var r;if(e==null)return;const s=se(e);if(n===void 0)return s[""];if(typeof n=="string")return(r=s[n])!==null&&r!==void 0?r:s[""];if(Array.isArray(n)){for(let t=n.length-1;t>=0;--t){const i=n[t];if(i in s)return s[i]}return s[""]}else{let t,i=-1;return Object.keys(s).forEach(l=>{const o=Number(l);!Number.isNaN(o)&&n>=o&&o>=i&&(i=o,t=s[l])}),t}}const re={xs:0,s:640,m:1024,l:1280,xl:1536,"2xl":1920};function ie(e){return`(min-width: ${e}px)`}const G={};function ae(e=re){if(!q)return g(()=>[]);if(typeof window.matchMedia!="function")return g(()=>[]);const n=N({}),r=Object.keys(e),s=(t,i)=>{t.matches?n.value[i]=!0:n.value[i]=!1};return r.forEach(t=>{const i=e[t];let l,o;G[i]===void 0?(l=window.matchMedia(ie(i)),l.addEventListener?l.addEventListener("change",d=>{o.forEach(f=>{f(d,t)})}):l.addListener&&l.addListener(d=>{o.forEach(f=>{f(d,t)})}),o=new Set,G[i]={mql:l,cbs:o}):(l=G[i].mql,o=G[i].cbs),o.add(s),l.matches&&o.forEach(d=>{d(l,t)})}),F(()=>{r.forEach(t=>{const{cbs:i}=G[e[t]];i.has(s)&&i.delete(s)})}),g(()=>{const{value:t}=n;return r.filter(i=>t[i])})}function le(e){var n;const r=(n=e.dirs)===null||n===void 0?void 0:n.find(({dir:s})=>s===V);return!!(r&&r.value===!1)}const T=1,L=U("n-grid"),Q=1,oe={span:{type:[Number,String],default:Q},offset:{type:[Number,String],default:0},suffix:Boolean,privateOffset:Number,privateSpan:Number,privateColStart:Number,privateShow:{type:Boolean,default:!0}},pe=O({__GRID_ITEM__:!0,name:"GridItem",alias:["Gi"],props:oe,setup(){const{isSsrRef:e,xGapRef:n,itemStyleRef:r,overflowRef:s,layoutShiftDisabledRef:t}=z(L),i=H();return{overflow:s,itemStyle:r,layoutShiftDisabled:t,mergedXGap:g(()=>x(n.value||0)),deriveStyle:()=>{e.value;const{privateSpan:l=Q,privateShow:o=!0,privateColStart:d=void 0,privateOffset:f=0}=i.vnode.props,{value:$}=n,S=x($||0);return{display:o?"":"none",gridColumn:`${d??`span ${l}`} / span ${l}`,marginLeft:f?`calc((100% - (${l} - 1) * ${S}) / ${l} * ${f} + ${S} * ${f})`:""}}}},render(){var e,n;if(this.layoutShiftDisabled){const{span:r,offset:s,mergedXGap:t}=this;return E("div",{style:{gridColumn:`span ${r} / span ${r}`,marginLeft:s?`calc((100% - (${r} - 1) * ${t}) / ${r} * ${s} + ${t} * ${s})`:""}},this.$slots)}return E("div",{style:[this.itemStyle,this.deriveStyle()]},(n=(e=this.$slots).default)===null||n===void 0?void 0:n.call(e,{overflow:this.overflow}))}}),fe={xs:0,s:640,m:1024,l:1280,xl:1536,xxl:1920},X=24,M="__ssr__",ue={layoutShiftDisabled:Boolean,responsive:{type:[String,Boolean],default:"self"},cols:{type:[Number,String],default:X},itemResponsive:Boolean,collapsed:Boolean,collapsedRows:{type:Number,default:1},itemStyle:[Object,String],xGap:{type:[Number,String],default:0},yGap:{type:[Number,String],default:0}},ce=O({name:"Grid",inheritAttrs:!1,props:ue,setup(e){const{mergedClsPrefixRef:n,mergedBreakpointsRef:r}=J(e),s=/^\d+$/,t=N(void 0),i=ae(r?.value||fe),l=B(()=>!!(e.itemResponsive||!s.test(e.cols.toString())||!s.test(e.xGap.toString())||!s.test(e.yGap.toString()))),o=g(()=>{if(l.value)return e.responsive==="self"?t.value:i.value}),d=B(()=>{var u;return(u=Number(_(e.cols.toString(),o.value)))!==null&&u!==void 0?u:X}),f=B(()=>_(e.xGap.toString(),o.value)),$=B(()=>_(e.yGap.toString(),o.value)),S=u=>{t.value=u.contentRect.width},v=u=>{te(S,u)},y=N(!1),R=g(()=>{if(e.responsive==="self")return v}),p=N(!1),h=N();return W(()=>{const{value:u}=h;u&&u.hasAttribute(M)&&(u.removeAttribute(M),p.value=!0)}),Z(L,{layoutShiftDisabledRef:j(e,"layoutShiftDisabled"),isSsrRef:p,itemStyleRef:j(e,"itemStyle"),xGapRef:f,overflowRef:y}),{isSsr:!K,contentEl:h,mergedClsPrefix:n,style:g(()=>e.layoutShiftDisabled?{width:"100%",display:"grid",gridTemplateColumns:`repeat(${e.cols}, minmax(0, 1fr))`,columnGap:x(e.xGap),rowGap:x(e.yGap)}:{width:"100%",display:"grid",gridTemplateColumns:`repeat(${d.value}, minmax(0, 1fr))`,columnGap:x(f.value),rowGap:x($.value)}),isResponsive:l,responsiveQuery:o,responsiveCols:d,handleResize:R,overflow:y}},render(){if(this.layoutShiftDisabled)return E("div",k({ref:"contentEl",class:`${this.mergedClsPrefix}-grid`,style:this.style},this.$attrs),this.$slots);const e=()=>{var n,r,s,t,i,l,o;this.overflow=!1;const d=ee(ne(this)),f=[],{collapsed:$,collapsedRows:S,responsiveCols:v,responsiveQuery:y}=this;d.forEach(a=>{var C,m,c,b,D;if(((C=a?.type)===null||C===void 0?void 0:C.__GRID_ITEM__)!==!0)return;if(le(a)){const w=P(a);w.props?w.props.privateShow=!1:w.props={privateShow:!1},f.push({child:w,rawChildSpan:0});return}a.dirs=((m=a.dirs)===null||m===void 0?void 0:m.filter(({dir:w})=>w!==V))||null,((c=a.dirs)===null||c===void 0?void 0:c.length)===0&&(a.dirs=null);const I=P(a),A=Number((D=_((b=I.props)===null||b===void 0?void 0:b.span,y))!==null&&D!==void 0?D:T);A!==0&&f.push({child:I,rawChildSpan:A})});let R=0;const p=(n=f[f.length-1])===null||n===void 0?void 0:n.child;if(p?.props){const a=(r=p.props)===null||r===void 0?void 0:r.suffix;a!==void 0&&a!==!1&&(R=Number((t=_((s=p.props)===null||s===void 0?void 0:s.span,y))!==null&&t!==void 0?t:T),p.props.privateSpan=R,p.props.privateColStart=v+1-R,p.props.privateShow=(i=p.props.privateShow)!==null&&i!==void 0?i:!0)}let h=0,u=!1;for(const{child:a,rawChildSpan:C}of f){if(u&&(this.overflow=!0),!u){const m=Number((o=_((l=a.props)===null||l===void 0?void 0:l.offset,y))!==null&&o!==void 0?o:0),c=Math.min(C+m,v);if(a.props?(a.props.privateSpan=c,a.props.privateOffset=m):a.props={privateSpan:c,privateOffset:m},$){const b=h%v;c+b>v&&(h+=v-b),c+h+R>S*v?u=!0:h+=c}}u&&(a.props?a.props.privateShow!==!0&&(a.props.privateShow=!1):a.props={privateShow:!1})}return E("div",k({ref:"contentEl",class:`${this.mergedClsPrefix}-grid`,style:this.style,[M]:this.isSsr||void 0},this.$attrs),f.map(({child:a})=>a))};return this.isResponsive&&this.responsive==="self"?E(Y,{onResize:this.handleResize},{default:e}):e()}});export{ce as _,pe as a};

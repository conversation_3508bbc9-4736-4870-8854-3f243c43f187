<!-- <script setup lang="ts">
import { useAppStore } from '@/store'

const appStore = useAppStore()
const options = [
  {
    label: 'ไทย',
    value: 'thTH',
  },
  {
    label: 'English',
    value: 'enUS',
  },
  // {
  //   label: '中文',
  //   value: 'zhCN',
  // },
]
</script>

<template>
  <n-popselect :value="appStore.lang" :options="options" trigger="click" @update:value="appStore.setAppLang">
    <CommonWrapper>
      <icon-park-outline-translate />
    </CommonWrapper>
  </n-popselect>
</template>

<style scoped></style> -->
<script setup lang="tsx">
import { useAppStore } from "@/store";
const { locale } = useI18n();
const {t} = useI18n();
const appStore = useAppStore();
const options = computed(()=>[
  {
    label: "ไทย",
    value: "thTH",
    icon: "/images/country/th.webp",
  },
  {
    label: "English",
    value: "enUS",
    icon: "/images/country/us.webp",
  },
]);

function renderLabel(option) {
  return (
    <div class="flex items-center gap-2">
      <img src={option.icon} alt={`${option.label} icon`} class="w-5 h-5" />
      <span>{option.label}</span>
    </div>
  );
}


</script>

<template>
  <!-- <div class="hidden sm:block">
  <n-popselect
    :value="appStore.lang"
    :options="options"
    trigger="click"
    @update:value="appStore.setAppLang"
    :render-label="renderLabel"
  >
    <CommonWrapper>
      <div class="w-15">
      <n-button size="small" type="primary"    class="flex">
      <icon-park-outline-translate />
      <img
        v-if="locale === 'thTH'"
        class="ml-1 "
        src="/images/country/th.webp"
        alt="Language Icon"
        width="20"
        height="20"
      />

      <img
        v-else-if="locale === 'enUS'"
        class="ml-1"
        src="/images/country/us.webp"
        alt="Language Icon"
        width="20"
        height="20"
      /></n-button>
      </div>
    </CommonWrapper>
  </n-popselect>
</div> -->

<div >
  <n-popselect
    :value="appStore.lang"
    :options="options"
    trigger="click"
    @update:value="appStore.setAppLang"
    :render-label="renderLabel"
  >
    <CommonWrapper>
       <div class="sm:w-5">
      <img
        v-if="locale === 'thTH'"
     
        src="/images/country/th.webp"
        alt="Language Icon"
        width="20"
        height="20"
      />

      <img
        v-else-if="locale === 'enUS'"
     
        src="/images/country/us.webp"
        alt="Language Icon"
        width="20"
        height="20"
      />
      </div>
    </CommonWrapper>
  </n-popselect>
</div>
</template>

<style scoped>
.flex {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>

<template>
  <div>
    <n-card>
      <n-space vertical size="large">
        <!-- Header Section -->
        <n-card>
          <n-space justify="space-between" align="center">
            <n-space align="center">
              <n-avatar color="#1a8a93">
                <icon-park-outline-dollar />
              </n-avatar>
              <div>
                <p class="text-lg font-medium">{{ $t("rateSettingManagement") }}</p>
                <n-text depth="3">{{ $t("manageExchangeRates") }}</n-text>
              </div>
            </n-space>
          </n-space>
        </n-card>

        <!-- Search Form -->
        <n-card>
          <n-form
            ref="formRef"
            :model="searchModel"
            :label-placement="isMobile ? 'top' : 'left'"
            :show-feedback="false"
          >
            <n-grid cols="1 600:2 1000:4" :x-gap="16" :y-gap="16">
              <n-gi>
                <n-form-item :label="$t('group')" path="groupName">
                  <n-select
                    v-model:value="searchModel.groupName"
                    :placeholder="$t('selectGroupName')"
                    clearable
                    :options="groupOptions"
                    label-field="title"
                    value-field="title"
                    filterable
                  >
                  </n-select>
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-form-item :label="$t('dateRange')" path="dateRange">
                  <n-config-provider :locale="localeDate" :date-locale="dateLocale">
                    <n-date-picker
                      v-model:value="searchModel.dateRange"
                      type="daterange"
                      clearable
                      format="dd/MM/yyyy"
                      style="width: 100%"
                    />
                  </n-config-provider>
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-form-item :label="' '" path="search">
                  <n-button type="default" @click="getData" block>
                    <template #icon>
                      <icon-park-outline-search />
                    </template>
                    {{ $t("search") }}
                  </n-button>
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-form-item :label="' '" path="refresh">
                  <n-button type="default" @click="handleRefresh" block secondary>
                    <template #icon>
                      <icon-park-outline-refresh />
                    </template>
                    {{ $t("refresh") }}
                  </n-button>
                </n-form-item>
              </n-gi>
            </n-grid>
          </n-form>
        </n-card>

        <!-- Data Table -->
          <n-data-table
            :scroll-x="1200"
            :columns="columns"
            :data="rateSettings"
            :loading="loading"
          />

          <div class="flex justify-end">
            <Pagination
              :count="total"
              @change="changePage"
            />
          </div>
      </n-space>
    </n-card>

    <!-- Edit Rate Modal -->
    <n-modal
      v-model:show="showEditModal"
      preset="dialog"
      :title="$t('editRate')"
    >
      <n-form
        ref="editFormRef"
        :model="editFormModel"
        :rules="rules"
        label-placement="top"
      >
        <n-form-item :label="$t('groupName')" path="groupName">
          <n-input
            v-model:value="editFormModel.groupName"
            size="large"
            disabled
            :placeholder="$t('enterGroupName')"
          />
        </n-form-item>

        <n-form-item :label="$t('rateTHBPerUSDT')" path="rateTHBPerUSDT">
          <n-input-number
            v-model:value="editFormModel.rateTHBPerUSDT"
            :min="1"
            :max="100"
            :step="0.01"
            :precision="2"
            size="large"
            style="width: 100%"
            :placeholder="$t('enterRate')"
          >
            <template #prefix>
              <img
                src="/images/country/th.webp"
                alt="THB"
                class="w-4 rounded mt-1 mr-2"
              />
            </template>
            <template #suffix>
              <span>฿ / USDT </span>
            </template>
          </n-input-number>
        </n-form-item>
      </n-form>

      <template #action>
        <n-space>
          <n-button @click="showEditModal = false">{{ $t("cancel") }}</n-button>
          <n-button type="primary" @click="handleEdit" :loading="submitting">
            {{ $t("save") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="tsx">
import { dateThTH, thTH, dateEnUS, enUS } from "naive-ui";
import http from "@/service/axios";
import moment from "moment-timezone";
const { t, locale } = useI18n();
const message = useMessage();
const isMobile = useMediaQuery("(max-width: 768px)");
const localeDate = ref(locale.value === "thTH" ? thTH : enUS);
const dateLocale = ref(locale.value === "thTH" ? dateThTH : dateEnUS);

// Watch for locale changes
watch(locale, (newLocale) => {
  localeDate.value = newLocale === "thTH" ? thTH : enUS;
  dateLocale.value = newLocale === "thTH" ? dateThTH : dateEnUS;
});

const loading = ref(false);
const submitting = ref(false);
const showEditModal = ref(false);
const total = ref(1);
const perPage = ref(10);
const Page = ref(1);

const searchModel = ref({
  groupName: null,
  dateRange: null,
});
const handleRefresh = () => {
  searchModel.value = {
    groupName: "",
    dateRange: null,
  };
};
const rateSettings = ref([]);

const editFormRef = ref();
const editFormModel = ref({
  _id: "",
  groupName: null,
  rateTHBPerUSDT: null,
  updatedByName: "",
  updatedByUsername: "",
});


const rules = {
  groupName: {
    required: true,
    message: t("groupNameRequired"),
    trigger: ["blur", "input"],
  },
  rateTHBPerUSDT: {
    required: true,
    type: "number",
    message: t("rateRequired"),
    trigger: ["blur", "change"],
  },
};

const columns = computed(() => [
  {
    title: t("no."),
    key: "index",
    align: "center",
    render: (row: any, index: number) => {
      return perPage.value * (Page.value - 1) + index + 1;
    },
  },
  {
    title: t("groupName"),
    key: "groupName",
    align: "center",
    render: (row: any) => {
      return (
        <div >
          {row.group_name || "-"}
        </div>
      );
    },
  },
  {
    title: t("rate"),
    key: "rateTHBPerUSDT",
    align: "center",
    render: (row: any) => {
      return (
        <div class="flex items-center justify-center gap-2">
          <img src="/images/country/th.webp" alt="THB" class="w-5 rounded" />
          <span class="font-semibold text-sm">
            {parseFloat(row.rateTHBPerUSDT?.toString() || "0").toFixed(2)} ฿
          </span>
          <span class="text-gray-500 text-xs">/ USDT</span>
        </div>
      );
    },
  },
  {
    title: t("updatedBy"),
    key: "updatedBy",
    align: "center",
    render: (row: any) => {
      return (
        <div class="text-center">
          <div class="font-medium text-sm">{row.updatedByName}</div>
          <div class="text-gray-500 text-xs">@{row.updatedByUsername}</div>
        </div>
      );
    },
  },
  {
    title: t("updatedAt"),
    key: "updatedAt",
    align: "center",
    render: (row: any) => {
      return (
        <div class="text-sm">
          {formatDate(row.updatedAt)}
        </div>
      );
    },
  },
  {
    title: t("actions"),
    key: "actions",
    align: "center",
    render: (row: any) => {
      return (
        <n-space justify="center">
          <n-button
            size="small"
            type="primary"
            onClick={() => handleEditClick(row)}
          >
          <div class="flex items-center gap-1">
              <icon-park-outline-edit /> {t("edit")}
            </div>
          </n-button>
        </n-space>
      );
    },
  },
]);

const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  return moment(dateString).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss");
};

const handleEditClick = (row: any) => {
  editFormModel.value = {
    _id: row._id,
    groupName: row.group_name,
    rateTHBPerUSDT: Number(row.rateTHBPerUSDT),
    updatedByName: row.updatedByName,
    updatedByUsername: row.updatedByUsername,
  };
  showEditModal.value = true;
};

const handleEdit = () => {
  editFormRef.value?.validate((errors: any) => {
    if (!errors) {
      submitting.value = true;
      const obj = {
        ID: editFormModel.value._id,
        groupName: editFormModel.value.groupName,
        rateTHBPerUSDT: editFormModel.value.rateTHBPerUSDT,
      };
      
      http
        .post("v1/rate-setting/update", obj)
        .then((response) => {
          if (response.data.success) {
            message.success(response.data.message || t("updatesuccess"));
            showEditModal.value = false;
            getData();
          } else {
            message.error(response.data.message || t("updatefailed"));
          }
        })
        .catch((error) => {
          message.error(error.response?.data?.message || t("updatefailed"));
        })
        .finally(() => {
          submitting.value = false;
        });
    }
  });
};

const getData = async () => {
  loading.value = true;
  try {
    const params = {
      page: Page.value,
      perPage: perPage.value,
      groupName: searchModel.value.groupName || undefined,
      startDate: searchModel.value.dateRange?.[0]
        ? moment(searchModel.value.dateRange[0]).format("YYYY-MM-DD")
        : undefined,
      endDate: searchModel.value.dateRange?.[1]
        ? moment(searchModel.value.dateRange[1]).format("YYYY-MM-DD")
        : undefined,
    };
    
    const {data: res} = await http.get("v1/rate-setting/list", { params });
      rateSettings.value = res.data
      total.value = res.total;
      loading.value = false;

  } catch (error) {
    message.error(t("fetchfailed"));
    loading.value = false;
  }
};

const changePage = (page: number, size: number) => {
  Page.value = page;
  perPage.value = size;
  getData();
};
const groupOptions = ref([]);
const getGroupList = async () => {
  try {
    const response = await http.get("v1/group-setting/getGroup");
    if (response.data.status) {
      groupOptions.value = response.data.data || [];
      return;
    }
  } catch (error) {
    message.error(t("fetchfailed"));
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  getData();
  getGroupList();
});
</script>

<style scoped></style>
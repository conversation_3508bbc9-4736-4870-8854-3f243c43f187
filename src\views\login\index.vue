<script setup lang="ts">
import FullScreen from "@/layouts/components/header/FullScreen.vue";
import { Login, Register, ResetPwd } from "./components";
import { useAppStore } from "@/store";
const appStore = useAppStore();
type IformType = "login" | "register" | "resetPwd";
const formType: Ref<IformType> = ref("login");
const formComponets = {
  login: Login,
  register: Register,
  resetPwd: ResetPwd,
};

const appName = import.meta.env.VITE_APP_NAME;
onMounted(() => {
  const isMobile = window.innerWidth <= 768;
  const canvas = document.getElementById("particles-canvas");
  const ctx = canvas.getContext("2d");

  const resizeCanvas = () => {
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
  };
  resizeCanvas();
  window.addEventListener("resize", resizeCanvas);

  const stars = [];
  const starCount = isMobile ? 30 : 100;

  for (let i = 0; i < starCount; i++) {
    stars.push({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      size: Math.random() * 2,
      opacity: Math.random() * 0.5 + 0.3,
      twinkleSpeed: Math.random() * 0.02,
      twinklePhase: Math.random() * Math.PI * 2,
      vx: (Math.random() - 0.5) * 0.2,
      vy: (Math.random() - 0.5) * 0.2,
    });
  }


  function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    stars.forEach((star, index) => {
      if (star.isShooting) {
        star.x += Math.cos(star.angle) * star.speed;
        star.y += Math.sin(star.angle) * star.speed;
        star.tail.unshift({ x: star.x, y: star.y });
        if (star.tail.length > star.maxTailLength) {
          star.tail.pop();
        }
        ctx.beginPath();
        ctx.moveTo(star.x, star.y);
        star.tail.forEach((point, i) => {
          const opacity = 1 - i / star.maxTailLength;
          const particleColor =
            appStore.colorMode === "dark" ? "255, 255, 255" : "0, 0, 0";
          ctx.strokeStyle = `rgba(${particleColor}, ${opacity})`;
          ctx.lineTo(point.x, point.y);
        });
        ctx.stroke();
        if (star.y > canvas.height || star.x > canvas.width) {
          star.x = Math.random() * canvas.width;
          star.y = 0;
          star.tail = [];
          star.angle = (Math.random() * 10 + 10) * (Math.PI / 180);
        }
      } else {
        star.x += star.vx;
        star.y += star.vy;
        if (star.x < 0) star.x = canvas.width;
        if (star.x > canvas.width) star.x = 0;
        if (star.y < 0) star.y = canvas.height;
        if (star.y > canvas.height) star.y = 0;
        star.twinklePhase += star.twinkleSpeed;
        const twinkle = Math.sin(star.twinklePhase) * 0.3 + 0.7;

        ctx.beginPath();
        ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
        const particleColor =
          appStore.colorMode === "dark" ? "255, 255, 255" : "0, 0, 0";
        ctx.fillStyle = `rgba(${particleColor}, ${star.opacity * twinkle})`;
        ctx.fill();
      }
    });

    requestAnimationFrame(animate);
  }

  animate();

  onUnmounted(() => {
    window.removeEventListener("resize", resizeCanvas);
  });
});
</script>

<template>
  <n-el class="wh-full flex-center" style="background-color: var(--body-color)">
    <canvas id="particles-canvas" class="fixed inset-0 w-full h-full pointer-events-none"></canvas>
    <div class="fixed top-15px sm:top-40px right-10px sm:right-40px text-lg z-100 rounded-xl"
      style="background-color: var(--body-color)">
      <div class="flex flex-col items-end ">
        <LangsSwitch />
        <DarkModeSwitch />
        <FullScreen />
      </div>
    </div>

    <n-el :class="[
        'py-lg sm:p-4xl h-full w-full z-10 sm:rounded-12px sm:bg-white  sm:dark:bg-[#18181c] sm:shadow-md duration-300 ',
        formType === 'register'
          ? 'sm:w-1440px sm:h-850px sm:flex-center'
          : 'sm:w-500px  sm:h-700px flex-center',
      ]">
      <div class="w-full flex flex-col items-center">
        <img :src="
            appStore.storeColorMode === 'light'
              ? '/images/logo/logo.png'
              : '/images/logo/logo.png'
          " class="w-150px h-auto rounded-full" alt="logo" />
        <n-h3>{{ appName }}</n-h3>
        <transition name="fade-slide" mode="out-in">
          <component :is="formComponets[formType]" v-model="formType" class="w-85%" />
        </transition>
      </div>
    </n-el>

    <div />
  </n-el>
</template>

<style scoped></style>

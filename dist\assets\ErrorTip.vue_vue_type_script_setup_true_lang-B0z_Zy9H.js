import{d as c,q as p,s as t,b as e,v as o,f as i,w as _,j as m,t as l,i as u,B as d}from"./index-DSmp6iCg.js";const y="/assets/error-403-VPEqmg7m.svg",f="/assets/error-404-DLMSXL2R.svg",g="/assets/error-500-C1R4JvdT.svg",h={class:"flex-col-center h-full"},k={key:0,src:y,alt:"",class:"w-1/3"},v={key:1,src:f,alt:"",class:"w-1/3"},w={key:2,src:g,alt:"",class:"w-1/3"},V=c({__name:"ErrorTip",props:{type:{}},setup(C){const a=p();return(s,r)=>{const n=d;return e(),t("div",h,[s.type==="403"?(e(),t("img",k)):o("",!0),s.type==="404"?(e(),t("img",v)):o("",!0),s.type==="500"?(e(),t("img",w)):o("",!0),i(n,{type:"primary",onClick:r[0]||(r[0]=b=>u(a).push("/dashboard/monitor"))},{default:_(()=>[m(l(s.$t("app.backHome")),1)]),_:1})])}}});export{V as _};

import{x as t,s as a,b as l,e}from"./index-DSmp6iCg.js";const n={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function r(s,o){return l(),a("svg",n,o[0]||(o[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M36.686 15.171a15.4 15.4 0 0 1 2.529 6.102H44v5.454h-4.785a15.4 15.4 0 0 1-2.529 6.102l3.385 3.385l-3.857 3.857l-3.385-3.385a15.4 15.4 0 0 1-6.102 2.529V44h-5.454v-4.785a15.4 15.4 0 0 1-6.102-2.529l-3.385 3.385l-3.857-3.857l3.385-3.385a15.4 15.4 0 0 1-2.529-6.102H4v-5.454h4.785a15.4 15.4 0 0 1 2.529-6.102l-3.385-3.385l3.857-3.857l3.385 3.385a15.4 15.4 0 0 1 6.102-2.529V4h5.454v4.785a15.4 15.4 0 0 1 6.102 2.529l3.385-3.385l3.857 3.857z"}),e("path",{d:"M24 29a5 5 0 1 0 0-10a5 5 0 0 0 0 10Z"})],-1)]))}const c=t({name:"icon-park-outline-setting",render:r});export{c as _};

import{_ as ve,a as he}from"./Pagination.vue_vue_type_script_setup_true_lang-uFMUKmhT.js";import{x as Q,s as S,b as f,e as d,d as ye,y as be,z as xe,A as ke,r as i,ff as P,fg as G,I as L,C as Re,D as V,f as t,E as z,j as c,o as Ne,H as k,w as n,G as $e,J as Ae,K as De,t as _,B as Te,i as l,fe as Me,L as Ue,fh as Se,M as we,R as Ee,l as Be,c as I,v as O}from"./index-DSmp6iCg.js";import{m as U}from"./index-BqRpacQ4.js";import{t as J,d as K,_ as Fe}from"./InputNumber-B2VNskBn.js";import{_ as He}from"./text-Df11blX7.js";import{_ as Ye,a as je}from"./FormItem-Be7I1W2B.js";import{_ as <PERSON>,a as Pe}from"./Grid-lBi2xqol.js";import{_ as Ge}from"./Input-D61U907N.js";import{_ as Le}from"./DatePicker-D6AT7i-o.js";import"./Checkbox-CuJqEfdi.js";import"./Forward-XU0gTivF.js";import"./moment-zH0z38ay.js";const Ve={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function ze(w,o){return f(),S("svg",Ve,o[0]||(o[0]=[d("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[d("path",{d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4S4 12.954 4 24s8.954 20 20 20Z"}),d("path",{"stroke-linecap":"round",d:"M24 16v16m-8-8h16"})],-1)]))}const Ie=Q({name:"icon-park-outline-add-one",render:ze}),Oe={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Je(w,o){return f(),S("svg",Oe,o[0]||(o[0]=[d("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[d("path",{d:"M39 6H9a3 3 0 0 0-3 3v30a3 3 0 0 0 3 3h30a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3"}),d("path",{d:"m21 31l5 4l8-10M14 15h20m-20 8h8"})],-1)]))}const Ke=Q({name:"icon-park-outline-transaction",render:Je}),Qe={class:"text-lg font-medium"},Ze={class:"flex gap-2"},it=ye({__name:"index",setup(w){const{t:o,locale:R}=be(),N=xe(),Z=ke(),E=i(R.value==="thTH"?J:P),B=i(R.value==="thTH"?K:G);L(R,e=>{E.value=e==="thTH"?J:P,B.value=e==="thTH"?K:G});const F=Re("(max-width: 768px)"),v=i(!1),H=i(1),$=i(10),A=i(1),m=i(!1),s=i({username:"",groupName:null,thbAmount:null,usdtAmount:null,exchangeRate:0}),r=i({username:"",groupName:null,type:"all",dateRange:null}),q=V(()=>[{label:o("all"),value:"all"},{label:"TRANSFER_OUT",value:"transfer"},{label:"BUY",value:"buy"}]),D=i([]),Y=i([]),W=V(()=>[{title:o("no."),key:"index",align:"center",render:(e,a)=>$.value*(A.value-1)+a+1},{title:o("username"),key:"username",align:"center",render:e=>t("div",{class:" text-sm"},[e.direction=="buy"?e.username:e.direction=="transfer_out"?e.by_transfer:"-"])},{title:o("type"),key:"type",align:"center",render:e=>t(z,{type:e.direction=="buy"?"success":"warning",class:" uppercase"},{default:()=>[e.direction||"-"]})},{title:o("groupName"),key:"groupName",align:"center",render:e=>t(z,{type:"default"},{default:()=>[e.group_name||"-"]})},{title:o("thbAmount"),key:"thbAmount",align:"center",render:e=>e.direction==="transfer_out"?t("div",{class:"flex items-center justify-center gap-2"},[t("span",{class:"font-semibold text-sm"},[c("-")])]):t("div",{class:"flex items-center justify-center gap-2"},[t("img",{src:"/images/country/th.webp",alt:"THB",class:"w-5 rounded"},null),t("span",{class:"font-semibold text-sm"},[parseFloat(e.fiatTHB?.toString()||"0").toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})," ",c("฿")])])},{title:o("usdtAmount"),key:"usdtAmount",align:"center",render:e=>t("div",{class:"flex items-center justify-center gap-2"},[t("span",{class:"font-semibold text-sm "},[parseFloat(e.usdtAmount?.toString()||"0").toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})]),t("span",{class:"text-gray-500 text-xs"},[c("USDT")])])},{title:o("exchangeRate"),key:"exchangeRate",align:"center",render:e=>e.direction==="transfer_out"?t("div",{class:"flex items-center justify-center gap-2"},[t("span",{class:"font-semibold text-sm"},[c("-")])]):t("div",{class:"flex items-center justify-center gap-2"},[t("span",{class:"font-semibold text-sm "},[parseFloat(e.rateTHBPerUSDT?.toString()||"0").toFixed(2)]),t("span",{class:"text-gray-500 text-xs"},[c("฿/USDT")])])},{title:o("exchangedAt"),key:"exchangedAt",align:"center",render:e=>t("div",{class:"text-sm"},[e.ts?X(e.ts):"-"])}]),X=e=>e?U(e).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss"):"-",h=async()=>{v.value=!0;try{const e={page:A.value,perPage:$.value,username:r.value.username||void 0,groupName:r.value.groupName||void 0,type:r.value.type||void 0,startDate:r.value.dateRange?.[0]?U(r.value.dateRange[0]).format("YYYY-MM-DD"):void 0,endDate:r.value.dateRange?.[1]?U(r.value.dateRange[1]).format("YYYY-MM-DD"):void 0},{data:a}=await k.get("v1/exchange/getExchange",{params:e});Y.value=a.data||[],H.value=a.total||0,v.value=!1}catch{N.error(o("fetchfailed")),v.value=!1}},ee=(e,a)=>{A.value=e,$.value=a,h()},te=()=>{s.value={username:"",groupName:null,thbAmount:null,usdtAmount:null,exchangeRate:0},m.value=!0},ae=async()=>{!s.value.exchangeRate||!s.value.groupName||!s.value.usdtAmount||Z.info({title:o("ยืนยันเพิ่มรายการแลกเปลี่ยน"),content:o("คุณต้องการเพิ่มรายการแลกเปลี่ยนหรือไม่ ?"),positiveText:o("confirmsave"),negativeText:o("cancel"),onPositiveClick:()=>{const e={exchangeRate:s.value.exchangeRate,groupName:s.value.groupName,usdtAmount:s.value.usdtAmount};k.post("v1/exchange/storeExchange",e).then(a=>{a.data.success&&(h(),N.success(o("saveDataSuccess")),m.value=!1)}).catch(a=>{N.error(o("deactivatefailed")),console.log(a)})}})};Ne(()=>{h(),ne()});const ne=()=>{k.get("v1/group-setting/getGroup").then(e=>{D.value=e.data.data})},le=e=>{const a={scope:e};k.get("v1/group-setting/getRateGroup",{params:a}).then(y=>{y.data.data&&(s.value.exchangeRate=y.data.data.rateTHBPerUSDT)})};return L([()=>s.value.thbAmount,()=>s.value.exchangeRate],([e,a])=>{e&&a&&e>0&&a>0?s.value.usdtAmount=parseFloat((e/a).toFixed(6)):s.value.usdtAmount=null},{immediate:!0}),(e,a)=>{const y=Ke,oe=De,se=He,g=$e,ue=Ie,b=Te,T=Ae,re=Me,ie=Ge,p=je,x=Pe,M=Ue,ce=Le,de=Se,pe=we,me=Ce,j=Ye,_e=ve,ge=he,C=Fe,fe=Ee;return f(),S("div",null,[t(T,null,{default:n(()=>[t(g,{vertical:"",size:"large"},{default:n(()=>[t(T,null,{default:n(()=>[t(g,{justify:"space-between",align:"center"},{default:n(()=>[t(g,{align:"center"},{default:n(()=>[t(oe,{color:"#1a8a93"},{default:n(()=>[t(y)]),_:1}),d("div",null,[d("p",Qe,_(e.$t("exchangeList")),1),t(se,{depth:"3"},{default:n(()=>[c(_(e.$t("manageExchangeTransactions")),1)]),_:1})])]),_:1}),t(b,{type:"primary",onClick:te},{icon:n(()=>[t(ue)]),default:n(()=>[c(" "+_(e.$t("addExchange")),1)]),_:1})]),_:1})]),_:1}),t(T,null,{default:n(()=>[t(j,{ref:"formRef",model:l(r),"label-placement":l(F)?"top":"left","show-feedback":!1},{default:n(()=>[t(me,{cols:"1 600:3 1000:4","x-gap":16,"y-gap":16},{default:n(()=>[t(x,null,{default:n(()=>[t(p,{label:e.$t("username"),path:"username"},{default:n(()=>[t(ie,{value:l(r).username,"onUpdate:value":a[0]||(a[0]=u=>l(r).username=u),placeholder:e.$t("enterUsername"),clearable:""},{prefix:n(()=>[t(re)]),_:1},8,["value","placeholder"])]),_:1},8,["label"])]),_:1}),t(x,null,{default:n(()=>[t(p,{label:e.$t("groupName"),path:"groupName"},{default:n(()=>[t(M,{value:l(r).groupName,"onUpdate:value":a[1]||(a[1]=u=>l(r).groupName=u),placeholder:e.$t("selectGroupName"),clearable:"",options:l(D),"label-field":"title","value-field":"title",filterable:""},null,8,["value","placeholder","options"])]),_:1},8,["label"])]),_:1}),t(x,null,{default:n(()=>[t(p,{label:e.$t("dateRange"),path:"dateRange"},{default:n(()=>[t(de,{locale:l(E),"date-locale":l(B)},{default:n(()=>[t(ce,{value:l(r).dateRange,"onUpdate:value":a[2]||(a[2]=u=>l(r).dateRange=u),type:"daterange",clearable:"",format:"dd/MM/yyyy"},null,8,["value"])]),_:1},8,["locale","date-locale"])]),_:1},8,["label"])]),_:1}),t(x,null,{default:n(()=>[d("div",Ze,[t(p,{label:e.$t("type"),path:"type"},{default:n(()=>[t(M,{value:l(r).type,"onUpdate:value":a[3]||(a[3]=u=>l(r).type=u),placeholder:e.$t("selectType"),clearable:"",options:l(q),class:"w-40"},null,8,["value","placeholder","options"])]),_:1},8,["label"]),t(b,{type:"default",onClick:h},{icon:n(()=>[t(pe)]),default:n(()=>[c(" "+_(e.$t("search")),1)]),_:1})])]),_:1})]),_:1})]),_:1},8,["model","label-placement"])]),_:1}),t(_e,{"scroll-x":1200,columns:l(W),data:l(Y),loading:l(v)},null,8,["columns","data","loading"]),t(g,{justify:"end"},{default:n(()=>[t(ge,{count:l(H),onChange:ee},null,8,["count"])]),_:1})]),_:1}),t(fe,{show:l(m),"onUpdate:show":a[9]||(a[9]=u=>Be(m)?m.value=u:null),preset:"dialog",title:e.$t("addExchange")},{action:n(()=>[t(g,{justify:"end"},{default:n(()=>[t(b,{onClick:a[8]||(a[8]=u=>m.value=!1)},{default:n(()=>[c(_(e.$t("cancel")),1)]),_:1}),t(b,{type:"primary",onClick:ae},{default:n(()=>[c(_(e.$t("save")),1)]),_:1})]),_:1})]),default:n(()=>[t(j,{ref:"addFormRef",model:l(s),"label-placement":l(F)?"top":"left",class:"mt-10"},{default:n(()=>[t(p,{label:e.$t("groupName"),path:"groupName"},{default:n(()=>[t(M,{value:l(s).groupName,"onUpdate:value":[a[4]||(a[4]=u=>l(s).groupName=u),a[5]||(a[5]=u=>le(l(s).groupName))],placeholder:e.$t("selectGroupName"),options:l(D),"value-field":"chatId","label-field":"title",filterable:"",clearable:""},null,8,["value","placeholder","options"])]),_:1},8,["label"]),l(s).groupName?(f(),I(p,{key:0,label:e.$t("exchangeRate"),path:"exchangeRate"},{default:n(()=>[t(C,{value:l(s).exchangeRate,"onUpdate:value":a[6]||(a[6]=u=>l(s).exchangeRate=u),min:0,step:.01,precision:2,style:{width:"100%"},placeholder:e.$t("enterExchangeRate"),disabled:"","show-button":!1},null,8,["value","placeholder"])]),_:1},8,["label"])):O("",!0),l(s).groupName?(f(),I(p,{key:1,label:e.$t("usdtAmount"),path:"usdtAmount"},{default:n(()=>[t(C,{value:l(s).usdtAmount,"onUpdate:value":a[7]||(a[7]=u=>l(s).usdtAmount=u),min:0,step:1,precision:0,style:{width:"100%"},placeholder:e.$t("enterUSDTAmount")},null,8,["value","placeholder"])]),_:1},8,["label"])):O("",!0)]),_:1},8,["model","label-placement"])]),_:1},8,["show","title"])]),_:1})])}}});export{it as default};

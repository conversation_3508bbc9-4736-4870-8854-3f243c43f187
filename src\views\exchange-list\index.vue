<template>
  <div>
    <n-card>
      <n-space vertical size="large">
        <!-- Header Section -->
        <n-card>
          <n-space justify="space-between" align="center">
            <n-space align="center">
              <n-avatar color="#1a8a93">
                <icon-park-outline-transaction />
              </n-avatar>
              <div>
                <p class="text-lg font-medium">{{ $t("exchangeList") }}</p>
                <n-text depth="3">{{
                  $t("manageExchangeTransactions")
                }}</n-text>
              </div>
            </n-space>
            <n-button type="primary" @click="handleAddExchange">
              <template #icon>
                <icon-park-outline-add-one />
              </template>
              {{ $t("addExchange") }}
            </n-button>
          </n-space>
        </n-card>

        <!-- Search Form -->
        <n-card>
          <n-form
            ref="formRef"
            :model="searchModel"
            :label-placement="isMobile ? 'top' : 'left'"
            :show-feedback="false"
          >
            <n-grid cols="1 600:3 1000:4" :x-gap="16" :y-gap="16">
              <n-gi>
                <n-form-item :label="$t('username')" path="username">
                  <n-input
                    v-model:value="searchModel.username"
                    :placeholder="$t('enterUsername')"
                    clearable
                  >
                    <template #prefix>
                      <icon-park-outline-user />
                    </template>
                  </n-input>
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-form-item :label="$t('groupName')" path="groupName">
                   <n-select
                    v-model:value="searchModel.groupName"
                    :placeholder="$t('selectGroupName')"
                    clearable
                    :options="groupOptions"
                    label-field="title"
                    value-field="title"
                    filterable
                  >
                  </n-select>
                 
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-form-item :label="$t('dateRange')" path="dateRange">
                  <n-config-provider
                    :locale="localeDate"
                    :date-locale="dateLocale"
                  >
                    <n-date-picker
                      v-model:value="searchModel.dateRange"
                      type="daterange"
                      clearable
                      format="dd/MM/yyyy"
                    />
                  </n-config-provider>
                </n-form-item>
              </n-gi>

              <n-gi>
                <div class="flex gap-2">
                  <n-form-item :label="$t('type')" path="type">
                    <n-select
                      v-model:value="searchModel.type"
                      :placeholder="$t('selectType')"
                      clearable
                      :options="exchangeTypes"
                      class="w-40"
                    />
                  </n-form-item>
                  <n-button type="default" @click="getData">
                    <template #icon>
                      <icon-park-outline-search />
                    </template>
                    {{ $t("search") }}
                  </n-button>
                </div>
              </n-gi>
            </n-grid>
          </n-form>
        </n-card>

        <!-- Data Table -->
        <n-data-table
          :scroll-x="1200"
          :columns="columns"
          :data="exchangeList"
          :loading="loading"
        />

        <!-- Pagination -->
        <n-space justify="end">
          <Pagination :count="total" @change="changePage" />
        </n-space>
      </n-space>

      <!-- Add Exchange Modal -->
      <n-modal
        v-model:show="addModal"
        preset="dialog"
        :title="$t('addExchange')"
      >
        <!-- Form -->
        <n-form
          ref="addFormRef"
          :model="addExchangeModel"
          :label-placement="isMobile ? 'top' : 'left'"
          class="mt-10"
        >
          <n-form-item :label="$t('groupName')" path="groupName">
            <n-select
              v-model:value="addExchangeModel.groupName"
              :placeholder="$t('selectGroupName')"
              :options="groupOptions"
              value-field="chatId"
              label-field="title"
              filterable
              clearable
              @update:value="searchData(addExchangeModel.groupName)"
            >
            </n-select>
          </n-form-item>

          <!-- <n-form-item :label="$t('type')" path="type">
            <n-select
              v-model:value="addExchangeModel.type"
              :placeholder="$t('selectType')"
              clearable
              :options="exchangeTypesAdd"
            />
          </n-form-item> -->
          <n-form-item
            v-if="addExchangeModel.groupName"
            :label="$t('exchangeRate')"
            path="exchangeRate"
          >
            <n-input-number
              v-model:value="addExchangeModel.exchangeRate"
              :min="0"
              :step="0.01"
              :precision="2"
              style="width: 100%"
              :placeholder="$t('enterExchangeRate')"
              disabled
              :show-button="false"
            >
            </n-input-number>
          </n-form-item>

          <n-form-item
            :label="$t('usdtAmount')"
            path="usdtAmount"
            v-if="addExchangeModel.groupName"
          >
            <n-input-number
              v-model:value="addExchangeModel.usdtAmount"
              :min="0"
              :step="1"
              :precision="0"
              style="width: 100%"
              :placeholder="$t('enterUSDTAmount')"
            >
            </n-input-number>
          </n-form-item>

          <!-- <n-form-item
            :label="$t('usdtAmount')"
            path="usdtAmount"
            v-if="addExchangeModel.groupName"
          >
            <n-input-number
              v-model:value="addExchangeModel.usdtAmount"
              :min="0"
              :step="0.000001"
              :precision="6"
              :show-button="false"
              style="width: 100%"
              placeholder="-"
            >
            </n-input-number>
          </n-form-item> -->
        </n-form>

        <template #action>
          <n-space justify="end">
            <n-button @click="addModal = false">{{ $t("cancel") }}</n-button>
            <n-button type="primary" @click="handleAddExchangeSubmit">
              {{ $t("save") }}
            </n-button>
          </n-space>
        </template>
      </n-modal>
    </n-card>
  </div>
</template>

<script setup lang="tsx">
import { dateThTH, thTH, dateEnUS, enUS } from "naive-ui";
import http from "@/service/axios";
import moment from "moment-timezone";
const { t, locale } = useI18n();
const message = useMessage();
const dialog = useDialog();
const localeDate = ref(locale.value === "thTH" ? thTH : enUS);
const dateLocale = ref(locale.value === "thTH" ? dateThTH : dateEnUS);
watch(locale, (newLocale) => {
  localeDate.value = newLocale === "thTH" ? thTH : enUS;
  dateLocale.value = newLocale === "thTH" ? dateThTH : dateEnUS;
});

const isMobile = useMediaQuery("(max-width: 768px)");
const loading = ref(false);
const total = ref(1);
const perPage = ref(10);
const Page = ref(1);
const addModal = ref(false);
const addExchangeModel = ref({
  username: "",
  groupName: null,
  // type: null,
  thbAmount: null,
  usdtAmount: null,
  exchangeRate: 0,
});
// Search
const searchModel = ref({
  username: "",
  groupName: null,
  type: "all",
  dateRange: null,
});

const exchangeTypes = computed(() => [
  { label: t("all"), value: "all" },
  { label: "TRANSFER_OUT", value: "transfer" },
  { label: "BUY", value: "buy" },
]);

// const exchangeTypesAdd = computed(() => [
//   { label: t("transfer"), value: "transfer" },
//   { label: t("buy"), value: "buy" },
// ]);

const groupOptions = ref([]);

const exchangeList = ref([]);

const columns = computed(() => [
  {
    title: t("no."),
    key: "index",
    align: "center",
    render: (row: any, index: number) => {
      return perPage.value * (Page.value - 1) + index + 1;
    },
  },
  {
    title: t("username"),
    key: "username",
    align: "center",
    render: (row: any) => {
      return (
        <div class=" text-sm">
          {row.direction == "buy"
            ? row.username
            : row.direction == "transfer_out"
            ? row.by_transfer
            : "-"}
        </div>
      );
    },
  },
  {
    title: t("type"),
    key: "type",
    align: "center",
    render: (row: any) => {
      return (
        <n-tag
          type={row.direction == "buy" ? "success" : "warning"}
          class=" uppercase"
        >
          {row.direction || "-"}
        </n-tag>
      );
    },
  },
  {
    title: t("groupName"),
    key: "groupName",
    align: "center",
    render: (row: any) => {
      return <n-tag type="default">{row.group_name || "-"}</n-tag>;
    },
  },
  {
    title: t("thbAmount"),
    key: "thbAmount",
    align: "center",
    render: (row: any) => {
      if (row.direction === "transfer_out") {
        return (
          <div class="flex items-center justify-center gap-2">
            <span class="font-semibold text-sm">-</span>
          </div>
        );
      }
      return (
        <div class="flex items-center justify-center gap-2">
          <img src="/images/country/th.webp" alt="THB" class="w-5 rounded" />
          <span class="font-semibold text-sm">
            {parseFloat(row.fiatTHB?.toString() || "0").toLocaleString(
              "en-US",
              { minimumFractionDigits: 2, maximumFractionDigits: 2 }
            )}{" "}
            ฿
          </span>
        </div>
      );
    },
  },
  {
    title: t("usdtAmount"),
    key: "usdtAmount",
    align: "center",
    render: (row: any) => {
      return (
        <div class="flex items-center justify-center gap-2">
          <span class="font-semibold text-sm ">
            {parseFloat(row.usdtAmount?.toString() || "0").toLocaleString(
              "en-US",
              { minimumFractionDigits: 2, maximumFractionDigits: 2 }
            )}
          </span>
          <span class="text-gray-500 text-xs">USDT</span>
        </div>
      );
    },
  },
  {
    title: t("exchangeRate"),
    key: "exchangeRate",
    align: "center",
    render: (row: any) => {
      if (row.direction === "transfer_out") {
        return (
          <div class="flex items-center justify-center gap-2">
            <span class="font-semibold text-sm">-</span>
          </div>
        );
      }
      return (
        <div class="flex items-center justify-center gap-2">
          <span class="font-semibold text-sm ">
            {parseFloat(row.rateTHBPerUSDT?.toString() || "0").toFixed(2)}
          </span>
          <span class="text-gray-500 text-xs">฿/USDT</span>
        </div>
      );
    },
  },
  {
    title: t("exchangedAt"),
    key: "exchangedAt",
    align: "center",
    render: (row: any) => {
      return <div class="text-sm">{row.ts ? formatDate(row.ts) : "-"}</div>;
    },
  },
]);

const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  return moment(dateString).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss");
};

const getData = async () => {
  loading.value = true;
  try {
    const params = {
      page: Page.value,
      perPage: perPage.value,
      username: searchModel.value.username || undefined,
      groupName: searchModel.value.groupName || undefined,
      type: searchModel.value.type || undefined,
      startDate: searchModel.value.dateRange?.[0]
        ? moment(searchModel.value.dateRange[0]).format("YYYY-MM-DD")
        : undefined,
      endDate: searchModel.value.dateRange?.[1]
        ? moment(searchModel.value.dateRange[1]).format("YYYY-MM-DD")
        : undefined,
    };

    const { data: res } = await http.get("v1/exchange/getExchange", { params });

    exchangeList.value = res.data || [];
    total.value = res.total || 0;
    loading.value = false;
  } catch (error) {
    message.error(t("fetchfailed"));
    loading.value = false;
  }
};

const changePage = (page: number, size: number) => {
  Page.value = page;
  perPage.value = size;
  getData();
};

const handleAddExchange = () => {
  // Reset form เมื่อเปิด modal
  addExchangeModel.value = {
    username: "",
    groupName: null,
    thbAmount: null,
    usdtAmount: null,
    exchangeRate: 0,
  };
  addModal.value = true;
};

const handleAddExchangeSubmit = async () => {
  if (
    !addExchangeModel.value.exchangeRate ||
    !addExchangeModel.value.groupName ||
    !addExchangeModel.value.usdtAmount
  ) {
    return;
  }
  dialog.info({
    title: t("ยืนยันเพิ่มรายการแลกเปลี่ยน"),
    content: t("คุณต้องการเพิ่มรายการแลกเปลี่ยนหรือไม่ ?"),
    positiveText: t("confirmsave"),
    negativeText: t("cancel"),
    onPositiveClick: () => {
      const obj = {
        exchangeRate: addExchangeModel.value.exchangeRate,
        groupName: addExchangeModel.value.groupName,
        // type: addExchangeModel.value.type,
        usdtAmount: addExchangeModel.value.usdtAmount,
      };
      http
        .post("v1/exchange/storeExchange", obj)
        .then((response) => {
          if (response.data.success) {
            getData();
            message.success(t("saveDataSuccess"));
            addModal.value = false
          }
        })
        .catch((err) => {
          message.error(t("deactivatefailed"));
          console.log(err);
        });
    },
  });
};


onMounted(() => {
  getData();
  getGroup();
});
const getGroup = () => {
  http.get("v1/group-setting/getGroup").then((response) => {
    groupOptions.value = response.data.data;
  });
};
const searchData = (item) => {
  const params = {
    scope: item,
  };
  http.get("v1/group-setting/getRateGroup", { params }).then((response) => {
    if (response.data.data) {
      addExchangeModel.value.exchangeRate = response.data.data.rateTHBPerUSDT;
    }
  });
};

watch(
  [
    () => addExchangeModel.value.thbAmount,
    () => addExchangeModel.value.exchangeRate,
  ],
  ([thbAmount, exchangeRate]) => {
    if (thbAmount && exchangeRate && thbAmount > 0 && exchangeRate > 0) {
      addExchangeModel.value.usdtAmount = parseFloat(
        (thbAmount / exchangeRate).toFixed(6)
      );
    } else {
      addExchangeModel.value.usdtAmount = null;
    }
  },
  { immediate: true }
);
</script>

<style scoped></style>

import{d as N,u as _,D as h,y as k,r as d,o as $,a9 as I,a as W,I as m,H as A,s as H,b as T,e as D,aa as p}from"./index-DSmp6iCg.js";import"./index-D3Jd_Dd9.js";import{h as P}from"./moment-zH0z38ay.js";const R={class:"-mt-5"},U=N({__name:"chart",props:["year"],setup(C){const S=C,b=_(),a=h(()=>b.primaryColor),{t:s,locale:n}=k();function f(t,o=1){const r=t.replace("#","");if(r.length!==6)return t;const e=parseInt(r,16),l=e>>16&255,F=e>>8&255,L=e&255;return`rgba(${l}, ${F}, ${L}, ${o})`}const v=h(()=>f(a.value,.2)),g=h(()=>f(a.value,.05)),z=h(()=>f(a.value,.03));let i=null;const y=d(null),B=h(()=>["chart.months.jan","chart.months.feb","chart.months.mar","chart.months.apr","chart.months.may","chart.months.jun","chart.months.jul","chart.months.aug","chart.months.sep","chart.months.oct","chart.months.nov","chart.months.dec"].map(o=>s(o))),c=()=>{if(i){const t={legend:{data:[s("chart.deposit"),s("chart.withdrawal")],top:"0",right:"5%",textStyle:{color:"#6B7280"}},tooltip:{axisPointer:{type:"cross",label:{backgroundColor:"#6a7985"}},trigger:"axis",backgroundColor:"#fff",borderRadius:10,padding:[10,14],borderColor:"#E5E7EB",borderWidth:1,textStyle:{color:"#374151",fontSize:13},formatter:o=>{let r=`<div style="font-weight:600;margin-bottom:6px;font-size:14px">${o[0].axisValue}</div>`;return o.forEach(e=>{const l=new Intl.NumberFormat(n.value==="thTH"?"th-TH":n.value==="zhCN"?"zh-CN":"en-US").format(e.value);r+=`<div style="color:${e.color};">${e.marker} ${e.seriesName} : ${l}</div>`}),r}},grid:u.value?{left:"3%",right:"4%",bottom:"15%",containLabel:!0}:{left:"4%",right:"4%",bottom:"4%",containLabel:!0},dataZoom:u.value?[{type:"inside",xAxisIndex:[0],start:0,end:60,zoomLock:!1},{type:"slider",xAxisIndex:[0],start:0,end:60,height:25,handleSize:"100%",handleStyle:{color:a.value}}]:[],xAxis:{type:"category",data:B.value,boundaryGap:!1,axisTick:{alignWithLabel:!0,lineStyle:{color:"#E5E7EB"}},axisLine:{lineStyle:{color:"#E5E7EB"}},axisLabel:{fontSize:13,color:"#6B7280",padding:[8,0]}},yAxis:{type:"value",splitLine:{lineStyle:{type:"dashed"}},axisLabel:{color:"#6B7280",fontSize:13,padding:[0,8],formatter:o=>new Intl.NumberFormat(n.value==="thTH"?"th-TH":n.value==="zhCN"?"zh-CN":"en-US",{notation:"compact",compactDisplay:"short"}).format(o)}},series:[{name:s("chart.deposit"),type:"line",smooth:!0,showSymbol:!0,symbol:"circle",symbolSize:7,emphasis:{focus:"series",itemStyle:{borderWidth:2,borderColor:a.value,shadowColor:z.value,shadowBlur:10}},lineStyle:{width:3,color:new p(0,0,1,0,[{offset:0,color:a.value},{offset:1,color:v.value}]),shadowColor:g.value,shadowBlur:12},areaStyle:{color:new p(0,0,0,1,[{offset:0,color:g.value},{offset:1,color:v.value}])},itemStyle:{color:a.value},data:w.value},{name:s("chart.withdrawal"),type:"line",smooth:!0,showSymbol:!0,symbol:"circle",symbolSize:7,emphasis:{focus:"series",itemStyle:{borderWidth:2,borderColor:"#FF4C4C",shadowColor:"rgba(255, 76, 76, 0.3)",shadowBlur:10}},lineStyle:{width:3,color:new p(0,0,1,0,[{offset:0,color:"#FF4C4C"},{offset:1,color:"rgba(255, 76, 76, 0.2)"}]),shadowColor:"rgba(255, 76, 76, 0.1)",shadowBlur:12},areaStyle:{color:new p(0,0,0,1,[{offset:0,color:"rgba(255, 76, 76, 0.2)"},{offset:1,color:"rgba(255, 76, 76, 0.05)"}])},itemStyle:{color:"#FF4C4C"},data:x.value}]};i.setOption(t)}},u=d(window.innerWidth<=768),w=d([]),x=d([]),E=d([]);return $(()=>{y.value&&(i=I(y.value),c()),window.addEventListener("resize",()=>{u.value=window.innerWidth<=768})}),W(()=>{i&&i.dispose()}),m(()=>u.value,c,{immediate:!0}),m(()=>b.primaryColor,c),m(()=>n.value,c),m(()=>S.year,t=>{if(t!=null){const r={year:P(t).format("YYYY")};A.get("v1/dashboard/getGraph",{params:r}).then(e=>{E.value=e.data,w.value=e.data.map(l=>Number(l.totalDeposit).toFixed(2)),x.value=e.data.map(l=>Number(l.totalWithdraw).toFixed(2)),c()}).catch(e=>{console.error("API Error:",e)})}},{immediate:!0}),(t,o)=>(T(),H("div",R,[D("div",{ref_key:"lineRef",ref:y,class:"h-400px w-full"},null,512)]))}});export{U as _};

import{d as Q,a0 as a,g1 as ir,Z as m,$ as l,fq as z,fv as K,fp as C,r as w,I as de,g2 as lr,D as F,g6 as sr,a1 as J,fR as se,g4 as ur,O as dr,gI as cr,a2 as fr,a3 as Ae,g_ as hr,fB as pr,X as vr,fN as ge,fO as gr,fM as br,fP as be,o as mr,gS as xr,Y as me,gl as wr,a5 as yr,fr as ue,g$ as Cr,a6 as zr,h0 as xe,h1 as Sr,fJ as Ar,fV as we,fW as ye,fU as y,gE as Ce,fS as ze}from"./index-DSmp6iCg.js";const _r=Q({name:"Eye",render(){return a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},a("path",{d:"M255.66 112c-77.94 0-157.89 45.11-220.83 135.33a16 16 0 0 0-.27 17.77C82.92 340.8 161.8 400 255.66 400c92.84 0 173.34-59.38 221.79-135.25a16.14 16.14 0 0 0 0-17.47C428.89 172.28 347.8 112 255.66 112z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"}),a("circle",{cx:"256",cy:"256",r:"80",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"}))}}),Rr=Q({name:"EyeOff",render(){return a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},a("path",{d:"M432 448a15.92 15.92 0 0 1-11.31-4.69l-352-352a16 16 0 0 1 22.62-22.62l352 352A16 16 0 0 1 432 448z",fill:"currentColor"}),a("path",{d:"M255.66 384c-41.49 0-81.5-12.28-118.92-36.5c-34.07-22-64.74-53.51-88.7-91v-.08c19.94-28.57 41.78-52.73 65.24-72.21a2 2 0 0 0 .14-2.94L93.5 161.38a2 2 0 0 0-2.71-.12c-24.92 21-48.05 46.76-69.08 76.92a31.92 31.92 0 0 0-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416a239.13 239.13 0 0 0 75.8-12.58a2 2 0 0 0 .77-3.31l-21.58-21.58a4 4 0 0 0-3.83-1a204.8 204.8 0 0 1-51.16 6.47z",fill:"currentColor"}),a("path",{d:"M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96a227.34 227.34 0 0 0-74.89 12.83a2 2 0 0 0-.75 3.31l21.55 21.55a4 4 0 0 0 3.88 1a192.82 192.82 0 0 1 50.21-6.69c40.69 0 80.58 12.43 118.55 37c34.71 22.4 65.74 53.88 89.76 91a.13.13 0 0 1 0 .16a310.72 310.72 0 0 1-64.12 72.73a2 2 0 0 0-.15 2.95l19.9 19.89a2 2 0 0 0 2.7.13a343.49 343.49 0 0 0 68.64-78.48a32.2 32.2 0 0 0-.1-34.78z",fill:"currentColor"}),a("path",{d:"M256 160a95.88 95.88 0 0 0-21.37 2.4a2 2 0 0 0-1 3.38l112.59 112.56a2 2 0 0 0 3.38-1A96 96 0 0 0 256 160z",fill:"currentColor"}),a("path",{d:"M165.78 233.66a2 2 0 0 0-3.38 1a96 96 0 0 0 115 115a2 2 0 0 0 1-3.38z",fill:"currentColor"}))}}),_e=ir("n-input"),Fr=m("input",`
 max-width: 100%;
 cursor: text;
 line-height: 1.5;
 z-index: auto;
 outline: none;
 box-sizing: border-box;
 position: relative;
 display: inline-flex;
 border-radius: var(--n-border-radius);
 background-color: var(--n-color);
 transition: background-color .3s var(--n-bezier);
 font-size: var(--n-font-size);
 font-weight: var(--n-font-weight);
 --n-padding-vertical: calc((var(--n-height) - 1.5 * var(--n-font-size)) / 2);
`,[l("input, textarea",`
 overflow: hidden;
 flex-grow: 1;
 position: relative;
 `),l("input-el, textarea-el, input-mirror, textarea-mirror, separator, placeholder",`
 box-sizing: border-box;
 font-size: inherit;
 line-height: 1.5;
 font-family: inherit;
 border: none;
 outline: none;
 background-color: #0000;
 text-align: inherit;
 transition:
 -webkit-text-fill-color .3s var(--n-bezier),
 caret-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 text-decoration-color .3s var(--n-bezier);
 `),l("input-el, textarea-el",`
 -webkit-appearance: none;
 scrollbar-width: none;
 width: 100%;
 min-width: 0;
 text-decoration-color: var(--n-text-decoration-color);
 color: var(--n-text-color);
 caret-color: var(--n-caret-color);
 background-color: transparent;
 `,[C("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",`
 width: 0;
 height: 0;
 display: none;
 `),C("&::placeholder",`
 color: #0000;
 -webkit-text-fill-color: transparent !important;
 `),C("&:-webkit-autofill ~",[l("placeholder","display: none;")])]),z("round",[K("textarea","border-radius: calc(var(--n-height) / 2);")]),l("placeholder",`
 pointer-events: none;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 overflow: hidden;
 color: var(--n-placeholder-color);
 `,[C("span",`
 width: 100%;
 display: inline-block;
 `)]),z("textarea",[l("placeholder","overflow: visible;")]),K("autosize","width: 100%;"),z("autosize",[l("textarea-el, input-el",`
 position: absolute;
 top: 0;
 left: 0;
 height: 100%;
 `)]),m("input-wrapper",`
 overflow: hidden;
 display: inline-flex;
 flex-grow: 1;
 position: relative;
 padding-left: var(--n-padding-left);
 padding-right: var(--n-padding-right);
 `),l("input-mirror",`
 padding: 0;
 height: var(--n-height);
 line-height: var(--n-height);
 overflow: hidden;
 visibility: hidden;
 position: static;
 white-space: pre;
 pointer-events: none;
 `),l("input-el",`
 padding: 0;
 height: var(--n-height);
 line-height: var(--n-height);
 `,[C("&[type=password]::-ms-reveal","display: none;"),C("+",[l("placeholder",`
 display: flex;
 align-items: center; 
 `)])]),K("textarea",[l("placeholder","white-space: nowrap;")]),l("eye",`
 display: flex;
 align-items: center;
 justify-content: center;
 transition: color .3s var(--n-bezier);
 `),z("textarea","width: 100%;",[m("input-word-count",`
 position: absolute;
 right: var(--n-padding-right);
 bottom: var(--n-padding-vertical);
 `),z("resizable",[m("input-wrapper",`
 resize: vertical;
 min-height: var(--n-height);
 `)]),l("textarea-el, textarea-mirror, placeholder",`
 height: 100%;
 padding-left: 0;
 padding-right: 0;
 padding-top: var(--n-padding-vertical);
 padding-bottom: var(--n-padding-vertical);
 word-break: break-word;
 display: inline-block;
 vertical-align: bottom;
 box-sizing: border-box;
 line-height: var(--n-line-height-textarea);
 margin: 0;
 resize: none;
 white-space: pre-wrap;
 scroll-padding-block-end: var(--n-padding-vertical);
 `),l("textarea-mirror",`
 width: 100%;
 pointer-events: none;
 overflow: hidden;
 visibility: hidden;
 position: static;
 white-space: pre-wrap;
 overflow-wrap: break-word;
 `)]),z("pair",[l("input-el, placeholder","text-align: center;"),l("separator",`
 display: flex;
 align-items: center;
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 white-space: nowrap;
 `,[m("icon",`
 color: var(--n-icon-color);
 `),m("base-icon",`
 color: var(--n-icon-color);
 `)])]),z("disabled",`
 cursor: not-allowed;
 background-color: var(--n-color-disabled);
 `,[l("border","border: var(--n-border-disabled);"),l("input-el, textarea-el",`
 cursor: not-allowed;
 color: var(--n-text-color-disabled);
 text-decoration-color: var(--n-text-color-disabled);
 `),l("placeholder","color: var(--n-placeholder-color-disabled);"),l("separator","color: var(--n-text-color-disabled);",[m("icon",`
 color: var(--n-icon-color-disabled);
 `),m("base-icon",`
 color: var(--n-icon-color-disabled);
 `)]),m("input-word-count",`
 color: var(--n-count-text-color-disabled);
 `),l("suffix, prefix","color: var(--n-text-color-disabled);",[m("icon",`
 color: var(--n-icon-color-disabled);
 `),m("internal-icon",`
 color: var(--n-icon-color-disabled);
 `)])]),K("disabled",[l("eye",`
 color: var(--n-icon-color);
 cursor: pointer;
 `,[C("&:hover",`
 color: var(--n-icon-color-hover);
 `),C("&:active",`
 color: var(--n-icon-color-pressed);
 `)]),C("&:hover",[l("state-border","border: var(--n-border-hover);")]),z("focus","background-color: var(--n-color-focus);",[l("state-border",`
 border: var(--n-border-focus);
 box-shadow: var(--n-box-shadow-focus);
 `)])]),l("border, state-border",`
 box-sizing: border-box;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 pointer-events: none;
 border-radius: inherit;
 border: var(--n-border);
 transition:
 box-shadow .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `),l("state-border",`
 border-color: #0000;
 z-index: 1;
 `),l("prefix","margin-right: 4px;"),l("suffix",`
 margin-left: 4px;
 `),l("suffix, prefix",`
 transition: color .3s var(--n-bezier);
 flex-wrap: nowrap;
 flex-shrink: 0;
 line-height: var(--n-height);
 white-space: nowrap;
 display: inline-flex;
 align-items: center;
 justify-content: center;
 color: var(--n-suffix-text-color);
 `,[m("base-loading",`
 font-size: var(--n-icon-size);
 margin: 0 2px;
 color: var(--n-loading-color);
 `),m("base-clear",`
 font-size: var(--n-icon-size);
 `,[l("placeholder",[m("base-icon",`
 transition: color .3s var(--n-bezier);
 color: var(--n-icon-color);
 font-size: var(--n-icon-size);
 `)])]),C(">",[m("icon",`
 transition: color .3s var(--n-bezier);
 color: var(--n-icon-color);
 font-size: var(--n-icon-size);
 `)]),m("base-icon",`
 font-size: var(--n-icon-size);
 `)]),m("input-word-count",`
 pointer-events: none;
 line-height: 1.5;
 font-size: .85em;
 color: var(--n-count-text-color);
 transition: color .3s var(--n-bezier);
 margin-left: 4px;
 font-variant: tabular-nums;
 `),["warning","error"].map(r=>z(`${r}-status`,[K("disabled",[m("base-loading",`
 color: var(--n-loading-color-${r})
 `),l("input-el, textarea-el",`
 caret-color: var(--n-caret-color-${r});
 `),l("state-border",`
 border: var(--n-border-${r});
 `),C("&:hover",[l("state-border",`
 border: var(--n-border-hover-${r});
 `)]),C("&:focus",`
 background-color: var(--n-color-focus-${r});
 `,[l("state-border",`
 box-shadow: var(--n-box-shadow-focus-${r});
 border: var(--n-border-focus-${r});
 `)]),z("focus",`
 background-color: var(--n-color-focus-${r});
 `,[l("state-border",`
 box-shadow: var(--n-box-shadow-focus-${r});
 border: var(--n-border-focus-${r});
 `)])])]))]),Br=m("input",[z("disabled",[l("input-el, textarea-el",`
 -webkit-text-fill-color: var(--n-text-color-disabled);
 `)])]);function $r(r){let x=0;for(const o of r)x++;return x}function Z(r){return r===""||r==null}function Er(r){const x=w(null);function o(){const{value:b}=r;if(!b?.focus){_();return}const{selectionStart:f,selectionEnd:s,value:d}=b;if(f==null||s==null){_();return}x.value={start:f,end:s,beforeText:d.slice(0,f),afterText:d.slice(s)}}function A(){var b;const{value:f}=x,{value:s}=r;if(!f||!s)return;const{value:d}=s,{start:u,beforeText:i,afterText:v}=f;let g=d.length;if(d.endsWith(v))g=d.length-v.length;else if(d.startsWith(i))g=i.length;else{const I=i[u-1],S=d.indexOf(I,u-1);S!==-1&&(g=S+1)}(b=s.setSelectionRange)===null||b===void 0||b.call(s,g,g)}function _(){x.value=null}return de(r,_),{recordCursor:o,restoreCursor:A}}const Se=Q({name:"InputWordCount",setup(r,{slots:x}){const{mergedValueRef:o,maxlengthRef:A,mergedClsPrefixRef:_,countGraphemesRef:b}=lr(_e),f=F(()=>{const{value:s}=o;return s===null||Array.isArray(s)?0:(b.value||$r)(s)});return()=>{const{value:s}=A,{value:d}=o;return a("span",{class:`${_.value}-input-word-count`},sr(x.default,{value:d===null||Array.isArray(d)?"":d},()=>[s===void 0?f.value:`${f.value} / ${s}`]))}}}),Pr=Object.assign(Object.assign({},Ae.props),{bordered:{type:Boolean,default:void 0},type:{type:String,default:"text"},placeholder:[Array,String],defaultValue:{type:[String,Array],default:null},value:[String,Array],disabled:{type:Boolean,default:void 0},size:String,rows:{type:[Number,String],default:3},round:Boolean,minlength:[String,Number],maxlength:[String,Number],clearable:Boolean,autosize:{type:[Boolean,Object],default:!1},pair:Boolean,separator:String,readonly:{type:[String,Boolean],default:!1},passivelyActivated:Boolean,showPasswordOn:String,stateful:{type:Boolean,default:!0},autofocus:Boolean,inputProps:Object,resizable:{type:Boolean,default:!0},showCount:Boolean,loading:{type:Boolean,default:void 0},allowInput:Function,renderCount:Function,onMousedown:Function,onKeydown:Function,onKeyup:[Function,Array],onInput:[Function,Array],onFocus:[Function,Array],onBlur:[Function,Array],onClick:[Function,Array],onChange:[Function,Array],onClear:[Function,Array],countGraphemes:Function,status:String,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],textDecoration:[String,Array],attrSize:{type:Number,default:20},onInputBlur:[Function,Array],onInputFocus:[Function,Array],onDeactivate:[Function,Array],onActivate:[Function,Array],onWrapperFocus:[Function,Array],onWrapperBlur:[Function,Array],internalDeactivateOnEnter:Boolean,internalForceFocus:Boolean,internalLoadingBeforeSuffix:{type:Boolean,default:!0},showPasswordToggle:Boolean}),Tr=Q({name:"Input",props:Pr,slots:Object,setup(r){const{mergedClsPrefixRef:x,mergedBorderedRef:o,inlineThemeDisabled:A,mergedRtlRef:_}=fr(r),b=Ae("Input","-input",Fr,Ar,r,x);hr&&pr("-input-safari",Br,x);const f=w(null),s=w(null),d=w(null),u=w(null),i=w(null),v=w(null),g=w(null),I=Er(g),S=w(null),{localeRef:Re}=vr("Input"),j=w(r.defaultValue),Fe=ge(r,"value"),R=gr(Fe,j),V=br(r),{mergedSizeRef:ee,mergedDisabledRef:T,mergedStatusRef:Be}=V,k=w(!1),W=w(!1),B=w(!1),D=w(!1);let ne=null;const re=F(()=>{const{placeholder:e,pair:n}=r;return n?Array.isArray(e)?e:e===void 0?["",""]:[e,e]:e===void 0?[Re.value.placeholder]:[e]}),$e=F(()=>{const{value:e}=B,{value:n}=R,{value:t}=re;return!e&&(Z(n)||Array.isArray(n)&&Z(n[0]))&&t[0]}),Ee=F(()=>{const{value:e}=B,{value:n}=R,{value:t}=re;return!e&&t[1]&&(Z(n)||Array.isArray(n)&&Z(n[1]))}),te=be(()=>r.internalForceFocus||k.value),Pe=be(()=>{if(T.value||r.readonly||!r.clearable||!te.value&&!W.value)return!1;const{value:e}=R,{value:n}=te;return r.pair?!!(Array.isArray(e)&&(e[0]||e[1]))&&(W.value||n):!!e&&(W.value||n)}),oe=F(()=>{const{showPasswordOn:e}=r;if(e)return e;if(r.showPasswordToggle)return"click"}),O=w(!1),Ie=F(()=>{const{textDecoration:e}=r;return e?Array.isArray(e)?e.map(n=>({textDecoration:n})):[{textDecoration:e}]:["",""]}),ce=w(void 0),Te=()=>{var e,n;if(r.type==="textarea"){const{autosize:t}=r;if(t&&(ce.value=(n=(e=S.value)===null||e===void 0?void 0:e.$el)===null||n===void 0?void 0:n.offsetWidth),!s.value||typeof t=="boolean")return;const{paddingTop:h,paddingBottom:p,lineHeight:c}=window.getComputedStyle(s.value),$=Number(h.slice(0,-2)),E=Number(p.slice(0,-2)),P=Number(c.slice(0,-2)),{value:N}=d;if(!N)return;if(t.minRows){const H=Math.max(t.minRows,1),le=`${$+E+P*H}px`;N.style.minHeight=le}if(t.maxRows){const H=`${$+E+P*t.maxRows}px`;N.style.maxHeight=H}}},ke=F(()=>{const{maxlength:e}=r;return e===void 0?void 0:Number(e)});mr(()=>{const{value:e}=R;Array.isArray(e)||ie(e)});const Me=xr().proxy;function U(e,n){const{onUpdateValue:t,"onUpdate:value":h,onInput:p}=r,{nTriggerFormInput:c}=V;t&&y(t,e,n),h&&y(h,e,n),p&&y(p,e,n),j.value=e,c()}function L(e,n){const{onChange:t}=r,{nTriggerFormChange:h}=V;t&&y(t,e,n),j.value=e,h()}function Ve(e){const{onBlur:n}=r,{nTriggerFormBlur:t}=V;n&&y(n,e),t()}function We(e){const{onFocus:n}=r,{nTriggerFormFocus:t}=V;n&&y(n,e),t()}function De(e){const{onClear:n}=r;n&&y(n,e)}function Oe(e){const{onInputBlur:n}=r;n&&y(n,e)}function Ne(e){const{onInputFocus:n}=r;n&&y(n,e)}function He(){const{onDeactivate:e}=r;e&&y(e)}function Ke(){const{onActivate:e}=r;e&&y(e)}function je(e){const{onClick:n}=r;n&&y(n,e)}function Ue(e){const{onWrapperFocus:n}=r;n&&y(n,e)}function Le(e){const{onWrapperBlur:n}=r;n&&y(n,e)}function Ge(){B.value=!0}function Xe(e){B.value=!1,e.target===v.value?G(e,1):G(e,0)}function G(e,n=0,t="input"){const h=e.target.value;if(ie(h),e instanceof InputEvent&&!e.isComposing&&(B.value=!1),r.type==="textarea"){const{value:c}=S;c&&c.syncUnifiedContainer()}if(ne=h,B.value)return;I.recordCursor();const p=Ye(h);if(p)if(!r.pair)t==="input"?U(h,{source:n}):L(h,{source:n});else{let{value:c}=R;Array.isArray(c)?c=[c[0],c[1]]:c=["",""],c[n]=h,t==="input"?U(c,{source:n}):L(c,{source:n})}Me.$forceUpdate(),p||we(I.restoreCursor)}function Ye(e){const{countGraphemes:n,maxlength:t,minlength:h}=r;if(n){let c;if(t!==void 0&&(c===void 0&&(c=n(e)),c>Number(t))||h!==void 0&&(c===void 0&&(c=n(e)),c<Number(t)))return!1}const{allowInput:p}=r;return typeof p=="function"?p(e):!0}function qe(e){Oe(e),e.relatedTarget===f.value&&He(),e.relatedTarget!==null&&(e.relatedTarget===i.value||e.relatedTarget===v.value||e.relatedTarget===s.value)||(D.value=!1),X(e,"blur"),g.value=null}function Je(e,n){Ne(e),k.value=!0,D.value=!0,Ke(),X(e,"focus"),n===0?g.value=i.value:n===1?g.value=v.value:n===2&&(g.value=s.value)}function Ze(e){r.passivelyActivated&&(Le(e),X(e,"blur"))}function Qe(e){r.passivelyActivated&&(k.value=!0,Ue(e),X(e,"focus"))}function X(e,n){e.relatedTarget!==null&&(e.relatedTarget===i.value||e.relatedTarget===v.value||e.relatedTarget===s.value||e.relatedTarget===f.value)||(n==="focus"?(We(e),k.value=!0):n==="blur"&&(Ve(e),k.value=!1))}function en(e,n){G(e,n,"change")}function nn(e){je(e)}function rn(e){De(e),fe()}function fe(){r.pair?(U(["",""],{source:"clear"}),L(["",""],{source:"clear"})):(U("",{source:"clear"}),L("",{source:"clear"}))}function tn(e){const{onMousedown:n}=r;n&&n(e);const{tagName:t}=e.target;if(t!=="INPUT"&&t!=="TEXTAREA"){if(r.resizable){const{value:h}=f;if(h){const{left:p,top:c,width:$,height:E}=h.getBoundingClientRect(),P=14;if(p+$-P<e.clientX&&e.clientX<p+$&&c+E-P<e.clientY&&e.clientY<c+E)return}}e.preventDefault(),k.value||he()}}function on(){var e;W.value=!0,r.type==="textarea"&&((e=S.value)===null||e===void 0||e.handleMouseEnterWrapper())}function an(){var e;W.value=!1,r.type==="textarea"&&((e=S.value)===null||e===void 0||e.handleMouseLeaveWrapper())}function ln(){T.value||oe.value==="click"&&(O.value=!O.value)}function sn(e){if(T.value)return;e.preventDefault();const n=h=>{h.preventDefault(),Ce("mouseup",document,n)};if(ye("mouseup",document,n),oe.value!=="mousedown")return;O.value=!0;const t=()=>{O.value=!1,Ce("mouseup",document,t)};ye("mouseup",document,t)}function un(e){r.onKeyup&&y(r.onKeyup,e)}function dn(e){switch(r.onKeydown&&y(r.onKeydown,e),e.key){case"Escape":ae();break;case"Enter":cn(e);break}}function cn(e){var n,t;if(r.passivelyActivated){const{value:h}=D;if(h){r.internalDeactivateOnEnter&&ae();return}e.preventDefault(),r.type==="textarea"?(n=s.value)===null||n===void 0||n.focus():(t=i.value)===null||t===void 0||t.focus()}}function ae(){r.passivelyActivated&&(D.value=!1,we(()=>{var e;(e=f.value)===null||e===void 0||e.focus()}))}function he(){var e,n,t;T.value||(r.passivelyActivated?(e=f.value)===null||e===void 0||e.focus():((n=s.value)===null||n===void 0||n.focus(),(t=i.value)===null||t===void 0||t.focus()))}function fn(){var e;!((e=f.value)===null||e===void 0)&&e.contains(document.activeElement)&&document.activeElement.blur()}function hn(){var e,n;(e=s.value)===null||e===void 0||e.select(),(n=i.value)===null||n===void 0||n.select()}function pn(){T.value||(s.value?s.value.focus():i.value&&i.value.focus())}function vn(){const{value:e}=f;e?.contains(document.activeElement)&&e!==document.activeElement&&ae()}function gn(e){if(r.type==="textarea"){const{value:n}=s;n?.scrollTo(e)}else{const{value:n}=i;n?.scrollTo(e)}}function ie(e){const{type:n,pair:t,autosize:h}=r;if(!t&&h)if(n==="textarea"){const{value:p}=d;p&&(p.textContent=`${e??""}\r
`)}else{const{value:p}=u;p&&(e?p.textContent=e:p.innerHTML="&nbsp;")}}function bn(){Te()}const pe=w({top:"0"});function mn(e){var n;const{scrollTop:t}=e.target;pe.value.top=`${-t}px`,(n=S.value)===null||n===void 0||n.syncUnifiedContainer()}let Y=null;me(()=>{const{autosize:e,type:n}=r;e&&n==="textarea"?Y=de(R,t=>{!Array.isArray(t)&&t!==ne&&ie(t)}):Y?.()});let q=null;me(()=>{r.type==="textarea"?q=de(R,e=>{var n;!Array.isArray(e)&&e!==ne&&((n=S.value)===null||n===void 0||n.syncUnifiedContainer())}):q?.()}),wr(_e,{mergedValueRef:R,maxlengthRef:ke,mergedClsPrefixRef:x,countGraphemesRef:ge(r,"countGraphemes")});const xn={wrapperElRef:f,inputElRef:i,textareaElRef:s,isCompositing:B,clear:fe,focus:he,blur:fn,select:hn,deactivate:vn,activate:pn,scrollTo:gn},wn=yr("Input",_,x),ve=F(()=>{const{value:e}=ee,{common:{cubicBezierEaseInOut:n},self:{color:t,borderRadius:h,textColor:p,caretColor:c,caretColorError:$,caretColorWarning:E,textDecorationColor:P,border:N,borderDisabled:H,borderHover:le,borderFocus:yn,placeholderColor:Cn,placeholderColorDisabled:zn,lineHeightTextarea:Sn,colorDisabled:An,colorFocus:_n,textColorDisabled:Rn,boxShadowFocus:Fn,iconSize:Bn,colorFocusWarning:$n,boxShadowFocusWarning:En,borderWarning:Pn,borderFocusWarning:In,borderHoverWarning:Tn,colorFocusError:kn,boxShadowFocusError:Mn,borderError:Vn,borderFocusError:Wn,borderHoverError:Dn,clearSize:On,clearColor:Nn,clearColorHover:Hn,clearColorPressed:Kn,iconColor:jn,iconColorDisabled:Un,suffixTextColor:Ln,countTextColor:Gn,countTextColorDisabled:Xn,iconColorHover:Yn,iconColorPressed:qn,loadingColor:Jn,loadingColorError:Zn,loadingColorWarning:Qn,fontWeight:er,[ue("padding",e)]:nr,[ue("fontSize",e)]:rr,[ue("height",e)]:tr}}=b.value,{left:or,right:ar}=Cr(nr);return{"--n-bezier":n,"--n-count-text-color":Gn,"--n-count-text-color-disabled":Xn,"--n-color":t,"--n-font-size":rr,"--n-font-weight":er,"--n-border-radius":h,"--n-height":tr,"--n-padding-left":or,"--n-padding-right":ar,"--n-text-color":p,"--n-caret-color":c,"--n-text-decoration-color":P,"--n-border":N,"--n-border-disabled":H,"--n-border-hover":le,"--n-border-focus":yn,"--n-placeholder-color":Cn,"--n-placeholder-color-disabled":zn,"--n-icon-size":Bn,"--n-line-height-textarea":Sn,"--n-color-disabled":An,"--n-color-focus":_n,"--n-text-color-disabled":Rn,"--n-box-shadow-focus":Fn,"--n-loading-color":Jn,"--n-caret-color-warning":E,"--n-color-focus-warning":$n,"--n-box-shadow-focus-warning":En,"--n-border-warning":Pn,"--n-border-focus-warning":In,"--n-border-hover-warning":Tn,"--n-loading-color-warning":Qn,"--n-caret-color-error":$,"--n-color-focus-error":kn,"--n-box-shadow-focus-error":Mn,"--n-border-error":Vn,"--n-border-focus-error":Wn,"--n-border-hover-error":Dn,"--n-loading-color-error":Zn,"--n-clear-color":Nn,"--n-clear-size":On,"--n-clear-color-hover":Hn,"--n-clear-color-pressed":Kn,"--n-icon-color":jn,"--n-icon-color-hover":Yn,"--n-icon-color-pressed":qn,"--n-icon-color-disabled":Un,"--n-suffix-text-color":Ln}}),M=A?zr("input",F(()=>{const{value:e}=ee;return e[0]}),ve,r):void 0;return Object.assign(Object.assign({},xn),{wrapperElRef:f,inputElRef:i,inputMirrorElRef:u,inputEl2Ref:v,textareaElRef:s,textareaMirrorElRef:d,textareaScrollbarInstRef:S,rtlEnabled:wn,uncontrolledValue:j,mergedValue:R,passwordVisible:O,mergedPlaceholder:re,showPlaceholder1:$e,showPlaceholder2:Ee,mergedFocus:te,isComposing:B,activated:D,showClearButton:Pe,mergedSize:ee,mergedDisabled:T,textDecorationStyle:Ie,mergedClsPrefix:x,mergedBordered:o,mergedShowPasswordOn:oe,placeholderStyle:pe,mergedStatus:Be,textAreaScrollContainerWidth:ce,handleTextAreaScroll:mn,handleCompositionStart:Ge,handleCompositionEnd:Xe,handleInput:G,handleInputBlur:qe,handleInputFocus:Je,handleWrapperBlur:Ze,handleWrapperFocus:Qe,handleMouseEnter:on,handleMouseLeave:an,handleMouseDown:tn,handleChange:en,handleClick:nn,handleClear:rn,handlePasswordToggleClick:ln,handlePasswordToggleMousedown:sn,handleWrapperKeydown:dn,handleWrapperKeyup:un,handleTextAreaMirrorResize:bn,getTextareaScrollContainer:()=>s.value,mergedTheme:b,cssVars:A?void 0:ve,themeClass:M?.themeClass,onRender:M?.onRender})},render(){var r,x;const{mergedClsPrefix:o,mergedStatus:A,themeClass:_,type:b,countGraphemes:f,onRender:s}=this,d=this.$slots;return s?.(),a("div",{ref:"wrapperElRef",class:[`${o}-input`,_,A&&`${o}-input--${A}-status`,{[`${o}-input--rtl`]:this.rtlEnabled,[`${o}-input--disabled`]:this.mergedDisabled,[`${o}-input--textarea`]:b==="textarea",[`${o}-input--resizable`]:this.resizable&&!this.autosize,[`${o}-input--autosize`]:this.autosize,[`${o}-input--round`]:this.round&&b!=="textarea",[`${o}-input--pair`]:this.pair,[`${o}-input--focus`]:this.mergedFocus,[`${o}-input--stateful`]:this.stateful}],style:this.cssVars,tabindex:!this.mergedDisabled&&this.passivelyActivated&&!this.activated?0:void 0,onFocus:this.handleWrapperFocus,onBlur:this.handleWrapperBlur,onClick:this.handleClick,onMousedown:this.handleMouseDown,onMouseenter:this.handleMouseEnter,onMouseleave:this.handleMouseLeave,onCompositionstart:this.handleCompositionStart,onCompositionend:this.handleCompositionEnd,onKeyup:this.handleWrapperKeyup,onKeydown:this.handleWrapperKeydown},a("div",{class:`${o}-input-wrapper`},J(d.prefix,u=>u&&a("div",{class:`${o}-input__prefix`},u)),b==="textarea"?a(ur,{ref:"textareaScrollbarInstRef",class:`${o}-input__textarea`,container:this.getTextareaScrollContainer,triggerDisplayManually:!0,useUnifiedContainer:!0,internalHoistYRail:!0},{default:()=>{var u,i;const{textAreaScrollContainerWidth:v}=this,g={width:this.autosize&&v&&`${v}px`};return a(dr,null,a("textarea",Object.assign({},this.inputProps,{ref:"textareaElRef",class:[`${o}-input__textarea-el`,(u=this.inputProps)===null||u===void 0?void 0:u.class],autofocus:this.autofocus,rows:Number(this.rows),placeholder:this.placeholder,value:this.mergedValue,disabled:this.mergedDisabled,maxlength:f?void 0:this.maxlength,minlength:f?void 0:this.minlength,readonly:this.readonly,tabindex:this.passivelyActivated&&!this.activated?-1:void 0,style:[this.textDecorationStyle[0],(i=this.inputProps)===null||i===void 0?void 0:i.style,g],onBlur:this.handleInputBlur,onFocus:I=>{this.handleInputFocus(I,2)},onInput:this.handleInput,onChange:this.handleChange,onScroll:this.handleTextAreaScroll})),this.showPlaceholder1?a("div",{class:`${o}-input__placeholder`,style:[this.placeholderStyle,g],key:"placeholder"},this.mergedPlaceholder[0]):null,this.autosize?a(cr,{onResize:this.handleTextAreaMirrorResize},{default:()=>a("div",{ref:"textareaMirrorElRef",class:`${o}-input__textarea-mirror`,key:"mirror"})}):null)}}):a("div",{class:`${o}-input__input`},a("input",Object.assign({type:b==="password"&&this.mergedShowPasswordOn&&this.passwordVisible?"text":b},this.inputProps,{ref:"inputElRef",class:[`${o}-input__input-el`,(r=this.inputProps)===null||r===void 0?void 0:r.class],style:[this.textDecorationStyle[0],(x=this.inputProps)===null||x===void 0?void 0:x.style],tabindex:this.passivelyActivated&&!this.activated?-1:void 0,placeholder:this.mergedPlaceholder[0],disabled:this.mergedDisabled,maxlength:f?void 0:this.maxlength,minlength:f?void 0:this.minlength,value:Array.isArray(this.mergedValue)?this.mergedValue[0]:this.mergedValue,readonly:this.readonly,autofocus:this.autofocus,size:this.attrSize,onBlur:this.handleInputBlur,onFocus:u=>{this.handleInputFocus(u,0)},onInput:u=>{this.handleInput(u,0)},onChange:u=>{this.handleChange(u,0)}})),this.showPlaceholder1?a("div",{class:`${o}-input__placeholder`},a("span",null,this.mergedPlaceholder[0])):null,this.autosize?a("div",{class:`${o}-input__input-mirror`,key:"mirror",ref:"inputMirrorElRef"}," "):null),!this.pair&&J(d.suffix,u=>u||this.clearable||this.showCount||this.mergedShowPasswordOn||this.loading!==void 0?a("div",{class:`${o}-input__suffix`},[J(d["clear-icon-placeholder"],i=>(this.clearable||i)&&a(xe,{clsPrefix:o,show:this.showClearButton,onClear:this.handleClear},{placeholder:()=>i,icon:()=>{var v,g;return(g=(v=this.$slots)["clear-icon"])===null||g===void 0?void 0:g.call(v)}})),this.internalLoadingBeforeSuffix?null:u,this.loading!==void 0?a(Sr,{clsPrefix:o,loading:this.loading,showArrow:!1,showClear:!1,style:this.cssVars}):null,this.internalLoadingBeforeSuffix?u:null,this.showCount&&this.type!=="textarea"?a(Se,null,{default:i=>{var v;const{renderCount:g}=this;return g?g(i):(v=d.count)===null||v===void 0?void 0:v.call(d,i)}}):null,this.mergedShowPasswordOn&&this.type==="password"?a("div",{class:`${o}-input__eye`,onMousedown:this.handlePasswordToggleMousedown,onClick:this.handlePasswordToggleClick},this.passwordVisible?se(d["password-visible-icon"],()=>[a(ze,{clsPrefix:o},{default:()=>a(_r,null)})]):se(d["password-invisible-icon"],()=>[a(ze,{clsPrefix:o},{default:()=>a(Rr,null)})])):null]):null)),this.pair?a("span",{class:`${o}-input__separator`},se(d.separator,()=>[this.separator])):null,this.pair?a("div",{class:`${o}-input-wrapper`},a("div",{class:`${o}-input__input`},a("input",{ref:"inputEl2Ref",type:this.type,class:`${o}-input__input-el`,tabindex:this.passivelyActivated&&!this.activated?-1:void 0,placeholder:this.mergedPlaceholder[1],disabled:this.mergedDisabled,maxlength:f?void 0:this.maxlength,minlength:f?void 0:this.minlength,value:Array.isArray(this.mergedValue)?this.mergedValue[1]:void 0,readonly:this.readonly,style:this.textDecorationStyle[1],onBlur:this.handleInputBlur,onFocus:u=>{this.handleInputFocus(u,1)},onInput:u=>{this.handleInput(u,1)},onChange:u=>{this.handleChange(u,1)}}),this.showPlaceholder2?a("div",{class:`${o}-input__placeholder`},a("span",null,this.mergedPlaceholder[1])):null),J(d.suffix,u=>(this.clearable||u)&&a("div",{class:`${o}-input__suffix`},[this.clearable&&a(xe,{clsPrefix:o,show:this.showClearButton,onClear:this.handleClear},{icon:()=>{var i;return(i=d["clear-icon"])===null||i===void 0?void 0:i.call(d)},placeholder:()=>{var i;return(i=d["clear-icon-placeholder"])===null||i===void 0?void 0:i.call(d)}}),u]))):null,this.mergedBordered?a("div",{class:`${o}-input__border`}):null,this.mergedBordered?a("div",{class:`${o}-input__state-border`}):null,this.showCount&&b==="textarea"?a(Se,null,{default:u=>{var i;const{renderCount:v}=this;return v?v(u):(i=d.count)===null||i===void 0?void 0:i.call(d,u)}}):null)}});export{Tr as _};

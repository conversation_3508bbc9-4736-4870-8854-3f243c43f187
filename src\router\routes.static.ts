export const staticRoutes: AppRoute.RowRoute[] = [
  {
    name: "dashboard",
    path: "/dashboard",
    title: "Dashboard",
    requiresAuth: true,
    icon: "icon-park-outline:analysis",
    menuType: "dir",
    componentPath: null,
    id: 1,
    pid: null,
  },
  {
    name: "monitor",
    path: "/dashboard/monitor",
    title: "Monitor",
    requiresAuth: true,
    icon: "icon-park-outline:anchor",
    menuType: "page",
    componentPath: "/dashboard/monitor/index.vue",
    id: 2,
    pid: 1,
  },

  {
    name: "exchange-list",
    path: "/exchange-list",
    title: "Exchange List",
    requiresAuth: true,
    icon: "icon-park-outline:order",
    menuType: "page",
    componentPath: "/exchange-list/index.vue",
    id: 3,
    pid: null,
  },

  {
    name: "group-list",
    path: "/group-list",
    title: "Group List",
    requiresAuth: true,
    icon: "icon-park-outline:connection-point",
    menuType: "page",
    componentPath: "/group-list/index.vue",
    id: 4,
    pid: null,
  },

  {
    name: "member-list",
    path: "/member-list",
    title: "Member List",
    requiresAuth: true,
    icon: "icon-park-outline:peoples",
    menuType: "page",
    componentPath: "/member-list/index.vue",
    id: 5,
    pid: null,
  },

  {
    name: "rate-setting",
    path: "/rate-setting",
    title: "Rate Setting",
    requiresAuth: true,
    icon: "icon-park-outline:setting-one",
    menuType: "page",
    componentPath: "/rate-setting/index.vue",
    id: 6,
    pid: null,
  },

  {
    name: "bank-list",
    path: "/bank-list",
    title: "Bank List",
    requiresAuth: true,
    icon: "icon-park-outline:bank-transfer",
    menuType: "page",
    componentPath: "/bank-list/index.vue",
    id: 7,
    pid: null,
  },

  {
    name: "employee-list",
    path: "/employee-list",
    title: "Employee List",
    requiresAuth: true,
    icon: "icon-park-outline:address-book",
    menuType: "page",
    componentPath: "/employee-list/index.vue",
    id: 8,
    pid: null,
  },
  {
    name: "my-profile",
    path: "/my-profile",
    title: "My Profile",
    requiresAuth: true,
    icon: "icon-park-outline:user-business",
    menuType: "page",
    componentPath: "/my-profile/index.vue",
    id: 99,
    pid: null,
    hide: true,
  },
];

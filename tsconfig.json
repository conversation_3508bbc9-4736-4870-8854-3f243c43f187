{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["ESNext", "DOM"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "node", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["node", "vite/client", "naive-ui/volar", "unplugin-icons/types/vue"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "exclude": ["node_modules", "dist"]}
import { router } from "@/router";
import { local } from "@/utils";
import { useRouteStore } from "./router";
import { useTabStore } from "./tab";
import http from "@/service/axios";
const message = useMessage();
interface AuthStatus {
  userData: Api.Login.Info | null;
  token: string;
}
export const useAuthStore = defineStore("auth-store", {
  state: (): AuthStatus => {
    return {
      userData: local.get("userData"),
      token: local.get("accessToken") || "",
    };
  },
  getters: {
    /** 是否登录 */
    isLogin(state) {
      return Boolean(state.token);
    },
  },
  actions: {
    /* 登录退出，重置用户信息等 */
    async logout() {
      const route = unref(router.currentRoute);
      const routeStore = useRouteStore();
      routeStore.resetRouteStore();
      if (route.meta.requiresAuth) {
        router.push({
          name: "login",
          query: {
            redirect: route.fullPath,
          },
        });
      }
      // 清除本地缓存
      this.clearAuthStorage();
      // 清空路由、菜单等数据

      // 清空标签栏数据
      const tabStore = useTabStore();
      tabStore.clearAllTabs();
      // 重置当前存储库
      this.$reset();
      // 重定向到登录页
    },
    clearAuthStorage() {
      local.remove("accessToken");
      local.remove("refreshToken");
      local.remove("userData");
    },

    login(username, password) {
      return http
        .post("auth/login", { username, password })
        .then((response) => {
          if (response.data.success) {
            const login_data = response.data.data;
            this.handleLoginInfo(login_data);
            return { success: true };
          } else {
            return { success: false, message: response.data.mes };
          }
        })
        .catch((error) => {
          return { success: false, message: error.message };
        });
    },
    async handleLoginInfo(data: Api.Login.Info) {
      local.set("userData", data);
      local.set("accessToken", data.token);
      local.set("refreshToken", data.refreshToken);
      this.token = data.token;
      this.userData = data;

      const routeStore = useRouteStore();
      await routeStore.initAuthRoute();

      // const route = unref(router.currentRoute)
      // const query = route.query as { redirect: string }
      router.push({
        path: "/dashboard/monitor",
      });
    },

    async refreshAccessToken() {
      const refreshToken = local.get("refreshToken");
      if (!refreshToken) {
        await this.logout();
        return null;
      }

      try {
        const response = await http.post("auth/refresh-token", {
          refreshToken: refreshToken,
        });

        if (response.data && response.data.status) {
          const newAccessToken = response.data.token;
          const newRefreshToken = response.data.refreshToken;

          local.set("accessToken", newAccessToken);
          if (newRefreshToken) {
            local.set("refreshToken", newRefreshToken);
          }

          this.token = newAccessToken;
          return newAccessToken;
        } else {
          await this.logout();
          return null;
        }
      } catch (error) {
        console.error("Refresh token failed:", error);
        await this.logout();
        return null;
      }
    }
  },
});

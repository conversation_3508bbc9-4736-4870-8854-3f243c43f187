<script setup lang="ts">
import { useAuthStore } from "@/store";
import { renderIcon } from "@/utils/icon";
import IconBookOpen from "~icons/icon-park-outline/book-open";
import IconGithub from "~icons/icon-park-outline/github";
import IconLogout from "~icons/icon-park-outline/logout";
import IconUser from "~icons/icon-park-outline/user";

const { t } = useI18n();

const { userData, logout } = useAuthStore();
const router = useRouter();

const options = computed(() => {
  return [
    {
      label: t("app.userCenter"),
      key: "userCenter",
      icon: () => h(IconUser),
    },
    {
      type: "divider",
      key: "d1",
    },
    {
      label: t("app.loginOut"),
      key: "loginOut",
      icon: () => h(IconLogout),
    },
  ];
});
function handleSelect(key: string | number) {
  if (key === "loginOut") {
    window.$dialog?.warning({
      onPositiveClick: () => {
        logout();
      },
      title: t("app.loginOutTitle"),
      content: t("app.loginOutContent"),
      positiveText: t("common.confirm"),
      negativeText: t("common.cancel"),
    });
  }
  if (key === "userCenter") router.push("/my-profile");
}
</script>

<template>
  <n-dropdown trigger="click" :options="options" @select="handleSelect">
    <n-avatar
      class="cursor-pointer mx-1"
      size="small"
      round
      src="/images/profile/profile.png"
    >
      <template #fallback>
        <div class="wh-full flex-center">
          <icon-park-outline-user />
        </div>
      </template>
    </n-avatar>
  </n-dropdown>
</template>

<style scoped></style>

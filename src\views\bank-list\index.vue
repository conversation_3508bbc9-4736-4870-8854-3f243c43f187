<script setup lang="tsx">
import http from "@/service/axios";
import moment from "moment";
const { t } = useI18n();
const message = useMessage();
const dialog = useDialog();
const formatDate = (date: string) => moment(date).format("YYYY-MM-DD HH:mm:ss");
const searchModel = ref({
  bank: null,
  active: null,
});
const isMobile = useMediaQuery("(max-width: 768px)");
const showModal = ref(false);
const itemsedit = ref([]);
const isEditing = ref(false);
const total = ref(1);
const perPage = ref(10);
const Page = ref(1);
const loading = ref(false);

const list = ref([]);

// Group options
const groupOptions = ref([]);

// Bank options with icons
const bankOptions = ref([]);

const getBankSelect = async (bank: string) => {
  try {
    const response = await http.get("v1/bank/getBankList", {
      params: { bank }
    });
    bankOptions.value = response.data.data;
  } catch (error) {
    message.error(t("fetchfailed"));
    console.error("Error fetching bank list:", error);
  }
};

const columns = computed(() => [
  {
    title: t("no."),
    key: "index",
    align: "center",
    render: (row: any, index: number) => {
      return perPage.value * (Page.value - 1) + index + 1;
    },
  },
  {
    title: t("group"),
    key: "group_name",
    align: "center",
    render: (row: any) => {
      return (
        <div class="text-center">
          <n-tag type="default" size="medium" class="mb-1">
            {row.group_name}
          </n-tag>
          <div class="text-xs">
            {row.data?.length || 0} {t("bank")}
          </div>
        </div>
      );
    },
  },
  {
    title: t("bank"),
    key: "bank",
    align: "center",
    render: (row: any) => {
      return (
        <div class="space-y-2">
          {row.data?.map((bank: any, index: number) => {
            const bankOption = bankOptions.value.find(
              (b) => b.value === bank.bank
            );
            return (
              <div
                key={index}
                class="flex items-center justify-between p-3  rounded-md border border-gray-200 dark:border-gray-500/20"
              >
                <div class="flex items-center gap-3">
                  <img
                    src={`/images/bankIcon/${bank.path_photo}`}
                    class="w-8 h-8 rounded"
                  />
                  <div>
                    <div class="font-medium text-sm text-start">
                      {bankOption?.label}
                    </div>
                    <div class="text-xs  font-mono">
                      {bank.accno} - {bank.name}
                    </div>
                  </div>
                </div>
                <n-tag type={bank.active ? "success" : "error"} size="small">
                  {bank.active ? t("active") : t("inactive")}
                </n-tag>
              </div>
            );
          })}
          {(!row.data || row.data.length === 0) && (
            <div class="text-center text-gray-400 py-4">
              <n-empty description={t("nobankAdded")} size="small" />
            </div>
          )}
        </div>
      );
    },
  },
  {
    title: t("updatedAt"),
    key: "updatedAt",
    align: "center",
    render: (row: any) => {
      return <div>{formatDate(row.updatedAt)}</div>;
    },
  },
  {
    title: t("updatedBy"),
    key: "updatedBy",
    align: "center",
    render: (row: any) => {
      return <div>{row.updatedBy || "-"}</div>;
    },
  },
  {
    title: t("manage"),
    align: "center",
    key: "actions",
    render: (row: any) => {
      return (
        <n-space justify="center" size="small">
          <n-button
            type="primary"
            size="small"
            onClick={() => handleEditGroup(row)}
          >
            <div class="flex items-center gap-1">
              <icon-park-outline-edit />
              {t("edit")}
            </div>
          </n-button>
          <n-button
            type="error"
            size="small"
            onClick={() => handleDeleteGroup(row)}
          >
            <div class="flex items-center gap-1">
              <icon-park-outline-delete />
              {t("delete")}{" "}
            </div>
          </n-button>
        </n-space>
      );
    },
  },
]);

const handleAddGroup = () => {
  itemsedit.value = {
    group_name: null,
    bank: [
      {
        type: "bank",        
        bank: null,
        accno: "",
        name: "",
        active: true,
        preferred: true,
        path_photo: null,
      },
    ],
  };
  isEditing.value = false;
  showModal.value = true;
};

const handleEditGroup = (item: any) => {
  console.log(item);
  itemsedit.value = {
    _id: item._id,
    group_name: item.group_name,
    scope: item.scope,
    bank: item.data,
  };
  isEditing.value = true;
  showModal.value = true;
};

const addBankEntry = () => {
  if (!itemsedit.value.bank) {
    itemsedit.value.bank = [];
  }
  itemsedit.value.bank.push({
    type: "bank",
    bank: null,
    accno: "",
    name: "",
    active: true,
    preferred: true,
    path_photo: null,
  });
};

const removeBankEntry = (index: number) => {
  if (itemsedit.value.bank && itemsedit.value.bank.length > 1) {
    itemsedit.value.bank.splice(index, 1);
  }
};

const handleDeleteGroup = (item: any) => {
  dialog.warning({
    title: t("confirmdelete"),
    content: t("areyousure"),
    positiveText: t("confirm"),
    negativeText: t("cancel"),
    onPositiveClick: () => {
      deleteGroup(item);
    },
  });
};

const deleteGroup = async (item: any) => {
  try {
    const response = await http.post("v1/bank/deleteBank", { ID: item._id });
    if (response.data.success) {
      message.success(response.data.mes);
      getData();
    } else {
      message.error(response.data.mes);
    }
  } catch (error) {
    message.error(t("deletefailed"));
  }
};

const SaveData = async () => {
  if (!itemsedit.value) return;

  // Validation
  if (!itemsedit.value.group_name?.trim()) {
    message.error(t("pleaseEnterGroupName"));
    return;
  }

  // Validate bank
  if (!itemsedit.value.bank || itemsedit.value.bank.length === 0) {
    message.error(t("pleaseAddAtLeastOneBank"));
    return;
  }

  for (let i = 0; i < itemsedit.value.bank.length; i++) {
    const bank = itemsedit.value.bank[i];
    if (!bank.bank) {
      message.error(t("pleaseSelectBankForEntry") + ` ${i + 1}`);
      return;
    }
    if (!bank.accno?.trim()) {
      message.error(t("pleaseEnterAccountNumberForEntry") + ` ${i + 1}`);
      return;
    }
    if (!bank.name?.trim()) {
      message.error(t("pleaseEnterAccountNameForEntry") + ` ${i + 1}`);
      return;
    }
  }

  try {
    const selectedGroup = groupOptions.value.find(
      (group) => group._id === itemsedit.value.group_name
    );
    console.log(selectedGroup);
    const obj = {
      group_name: selectedGroup?.title || itemsedit.value.group_name,
      data: itemsedit.value.bank,
      scope: selectedGroup?.chatId || itemsedit.value.scope,
    };

    if (isEditing.value) {
      obj.ID = itemsedit.value._id;
    }

    const endpoint = isEditing.value
      ? "v1/bank/storeBank"
      : "v1/bank/storeBank";
    const response = await http.post(endpoint, obj);

    if (response.data.success === true) {
      message.success(response.data.mes);
      showModal.value = false;
      getData();
    } else {
      message.error(response.data.mes);
    }
  } catch (error) {
    message.error(t("savefailed"));
  }
};
watch(() => itemsedit.value.bank, (newBanks) => {
  if (newBanks) {
    newBanks.forEach(bank => {
      if (bank.bank && !bank.path_photo) {
        const selectedBank = bankOptions.value.find(b => b.bankcode === bank.bank);
        if (selectedBank) {
          bank.path_photo = selectedBank.path_photo;
        }
      }
    });
  }
}, { deep: true });
const getData = async () => {
  try {
    loading.value = true;
    const params = {
      page: Page.value,
      limit: perPage.value,
      group_name: searchModel.value.group_name,
      bank_name: searchModel.value.bank,
    };
    const response = await http.get("v1/bank/getBank", { params });
    list.value = response.data.data || [];
    total.value = response.data.total || 1;
  } catch (error) {
    message.error(t("fetchfailed"));
  } finally {
    loading.value = false;
  }
};

const getGroupList = async () => {
  try {
    const response = await http.get("v1/group-setting/getGroup");
    if (response.data.status) {
      groupOptions.value = response.data.data || [];
      return;
    }
  } catch (error) {
    message.error(t("fetchfailed"));
  } finally {
    loading.value = false;
  }
};

const changePage = (page: number, size: number) => {
  perPage.value = size;
  Page.value = page;
  getData();
};

const renderBankLabel = (option: any) => {
  return (
    <div class="flex items-center gap-2">
      <img
        src={`/images/bankIcon/${option.path_photo}`}
        alt={option.name}
        class="w-5 h-5 rounded"
      />
      <span>{option.name}</span>
    </div>
  );
};
onMounted(() => {
  getData();
  getGroupList();
  getBankSelect();
});

const handleReset = () => {
  searchModel.value = {
    group_name: null,
    bank: null,
  };
};
</script>

<template>
  <div>
    <n-card>
      <n-space vertical size="large">
        <!-- Header Section -->
        <n-card>
          <n-space justify="space-between" align="center">
            <n-space align="center">
              <n-avatar color="#1a8a93">
                <icon-park-outline-bank-card />
              </n-avatar>
              <div>
                <p class="text-lg font-medium">{{ $t("banklist") }}</p>
                <n-text depth="3">{{ $t("manageBankAccounts") }}</n-text>
              </div>
            </n-space>
            <n-button type="primary" @click="handleAddGroup">
              <template #icon>
                <icon-park-outline-plus />
              </template>
              {{ t("addbank") }}
            </n-button>
          </n-space>
        </n-card>

        <!-- Search Form -->
        <n-card>
          <n-form
            ref="formRef"
            :model="searchModel"
            :label-placement="isMobile ? 'top' : 'left'"
            :show-feedback="false"
          >
            <n-grid cols="1 600:2 1000:4" :x-gap="16" :y-gap="16">
              <n-gi>
                <!-- {{ searchModel }} -->
                <n-form-item :label="t('group')" path="group_name">
                  <n-select
                    v-model:value="searchModel.group_name"
                    :options="groupOptions"
                    :placeholder="t('selectGroupName')"
                    filterable
                    clearable
                    label-field="title"
                    value-field="chatId"
                  >
                  </n-select>
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-form-item :label="t('bank')" path="bank">
                  <n-select
                    v-model:value="searchModel.bank"
                    :options="bankOptions"
                    :placeholder="t('selectBank')"
                    :render-label="renderBankLabel"
                    filterable
                    clearable
                  />
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-form-item :label="' '" path="search">
                  <n-button type="default" @click="getData" block>
                    <template #icon>
                      <icon-park-outline-search />
                    </template>
                    {{ t("search") }}
                  </n-button>
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-form-item :label="' '" path="refresh">
                  <n-button type="default" @click="handleReset" block secondary>
                    <template #icon>
                      <icon-park-outline-refresh />
                    </template>
                    {{ t("reset") }}
                  </n-button>
                </n-form-item>
              </n-gi>
            </n-grid>
          </n-form>
        </n-card>

        <!-- Data Table -->
        <n-data-table
          :scroll-x="1200"
          :columns="columns"
          :data="list"
          :loading="loading"
        />

        <!-- Pagination -->
        <div class="flex justify-end">
          <Pagination :count="total" @change="changePage" />
        </div>
      </n-space>
    </n-card>

    <!-- Modal -->
    <n-modal
      v-model:show="showModal"
      preset="dialog"
      :show-icon="false"
      style="width: 800px"
    >
      <template #header>
        <n-space align="center">
          <div class="flex items-center gap-2">
            <icon-park-outline-bank-card />
            {{ isEditing ? t("editbank") : t("addbank") }}
          </div>
        </n-space>
      </template>

      <n-form label-placement="top" class="mt-5">
        <!-- Group Name -->
        <n-form-item :label="t('group')" required>
          <n-select
            v-model:value="itemsedit.group_name"
            :options="groupOptions"
            :placeholder="t('selectGroupName')"
            filterable
            clearable
            label-field="title"
            value-field="_id"
             :disabled="isEditing"
          />
        </n-form-item>

        <!-- bank Section -->
        <n-form-item :label="t('bank')" required>
          <n-space vertical size="large" class="w-full">
            <n-card v-for="(bank, index) in itemsedit.bank" :key="index">
              <n-space justify="space-between" align="center" class="mb-3">
                <n-text strong>{{ t("bank") }} {{ index + 1 }}</n-text>
                <n-button
                  v-if="itemsedit.bank && itemsedit.bank.length > 1"
                  type="error"
                  size="small"
                  @click="removeBankEntry(index)"
                >
                  <template #icon>
                    <icon-park-outline-delete />
                  </template>
                  {{ t("delete") }}
                </n-button>
              </n-space>

              <n-grid cols="1 s:2" :x-gap="12" :y-gap="12" responsive="screen">
                <n-gi>
                  <n-form-item :label="t('bankName')" required>
                    <n-select
                      v-model:value="bank.bank"
                      :options="bankOptions"
                      :placeholder="t('selectBank')"
                      :render-label="renderBankLabel"
                      value-field="bankcode"
                      label-field="name"
                      clearable
                      filterable
                    />
                  </n-form-item>
                </n-gi>
                <n-gi>
                  <n-form-item :label="t('status')" required>
                    <n-switch
                      v-model:value="bank.active"
                      :checked-value="true"
                      :unchecked-value="false"
                    >
                      <template #checked>
                        {{ t("active") }}
                      </template>
                      <template #unchecked>
                        {{ t("inactive") }}
                      </template>
                    </n-switch>
                  </n-form-item>
                </n-gi>
                <n-gi>
                  <n-form-item :label="t('accountnumber')" required>
                    <n-input
                      type="number"
                      class="w-full"
                      :show-button="false"
                      v-model:value="bank.accno"
                      :placeholder="t('enterAccountNumber')"
                    />
                  </n-form-item>
                </n-gi>
                <n-gi>
                  <n-form-item :label="t('accountname')" required>
                    <n-input
                      v-model:value="bank.name"
                      :placeholder="t('enterAccountName')"
                    />
                  </n-form-item>
                </n-gi>
              </n-grid>
            </n-card>

            <n-button
              type="default"
              dashed
              block
              @click="addBankEntry"
              class="mt-5"
            >
              <template #icon>
                <icon-park-outline-plus />
              </template>
              {{ t("addbank") }}
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>

      <template #action>
        <n-space justify="end">
          <n-button @click="showModal = false">
            {{ t("cancel") }}
          </n-button>
          <n-button type="primary" @click="SaveData">
            {{ t("save") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

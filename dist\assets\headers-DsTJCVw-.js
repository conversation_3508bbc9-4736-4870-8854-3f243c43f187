import{Z as z,fp as d,fq as f,d as $,a0 as T,a2 as v,a3 as c,D as h,fr as a,a6 as R,fs as B}from"./index-DSmp6iCg.js";const H=z("h",`
 font-size: var(--n-font-size);
 font-weight: var(--n-font-weight);
 margin: var(--n-margin);
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
`,[d("&:first-child",{marginTop:0}),f("prefix-bar",{position:"relative",paddingLeft:"var(--n-prefix-width)"},[f("align-text",{paddingLeft:0},[d("&::before",{left:"calc(-1 * var(--n-prefix-width))"})]),d("&::before",`
 content: "";
 width: var(--n-bar-width);
 border-radius: calc(var(--n-bar-width) / 2);
 transition: background-color .3s var(--n-bezier);
 left: 0;
 top: 0;
 bottom: 0;
 position: absolute;
 `),d("&::before",{backgroundColor:"var(--n-bar-color)"})])]),P=Object.assign(Object.assign({},c.props),{type:{type:String,default:"default"},prefix:String,alignText:Boolean}),l=n=>$({name:`H${n}`,props:P,setup(e){const{mergedClsPrefixRef:i,inlineThemeDisabled:s}=v(e),t=c("Typography","-h",H,B,e,i),o=h(()=>{const{type:g}=e,{common:{cubicBezierEaseInOut:u},self:{headerFontWeight:p,headerTextColor:b,[a("headerPrefixWidth",n)]:m,[a("headerFontSize",n)]:x,[a("headerMargin",n)]:C,[a("headerBarWidth",n)]:y,[a("headerBarColor",g)]:w}}=t.value;return{"--n-bezier":u,"--n-font-size":x,"--n-margin":C,"--n-bar-color":w,"--n-bar-width":y,"--n-font-weight":p,"--n-text-color":b,"--n-prefix-width":m}}),r=s?R(`h${n}`,h(()=>e.type[0]),o,e):void 0;return{mergedClsPrefix:i,cssVars:s?void 0:o,themeClass:r?.themeClass,onRender:r?.onRender}},render(){var e;const{prefix:i,alignText:s,mergedClsPrefix:t,cssVars:o,$slots:r}=this;return(e=this.onRender)===null||e===void 0||e.call(this),T(`h${n}`,{class:[`${t}-h`,`${t}-h${n}`,this.themeClass,{[`${t}-h--prefix-bar`]:i,[`${t}-h--align-text`]:s}],style:o},r)}}),S=l("2"),L=l("3");export{L as N,S as a};

<script setup lang="ts">
import { useAppStore } from "@/store";

const router = useRouter();
const appStore = useAppStore();

const name = import.meta.env.VITE_APP_NAME;
</script>

<template>
  <div
    class="h-60px text-xl flex-center cursor-pointer gap-2 p-x-2"
    @click="router.push('/dashboard/monitor')"
  >
    <!-- <svg-icons-logo class="text-1.5em" /> -->
    <div class="h-[36px] w-[36px] flex-center">
      <img
        :src="
          appStore.storeColorMode === 'light'
            ? '/images/logo/logo.png'
            : '/images/logo/logo.png'
        "
        class="w-full h-auto rounded-full"
        alt="logo"
      />
    </div>
    <span
      v-show="!appStore.collapsed"
      class="text-ellipsis overflow-hidden whitespace-nowrap font-medium text-16px"
      >{{ name }}</span
    >
  </div>
</template>

<style scoped></style>

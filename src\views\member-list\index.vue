<script setup lang="tsx">
import http from "@/service/axios";
import moment from "moment-timezone";
import { useBoolean } from "@/hooks";

const { t } = useI18n();
const message = useMessage();
const dialog = useDialog();
const isMobile = useMediaQuery("(max-width: 768px)");

const {
  bool: loading,
  setTrue: startLoading,
  setFalse: endLoading,
} = useBoolean(false);

const model = ref({
  username: "",
  groupName: null,
});
const showModal = ref(false);
const itemsedit = ref({});
const isEditing = ref(false);
const total = ref(1);
const perPage = ref(10);
const Page = ref(1);
const list = ref([
]);

// Role options for the select dropdown
const roleOptions = computed(() => [
  { label: t("member"), value: "member" },
  { label: t("admin"), value: "admin" },
]);

const columns = computed(() => [
  {
    title: t("no."),
    align: "center",
    key: "index",
    render: (row, index) => {
      return perPage.value * (Page.value - 1) + index + 1;
    },
  },
  {
    title: t("userid"),
    key: "userId",
    align: "center",
    render: (row) => {
      return <div class="text-sm font-mono">{row.userId || "-"}</div>;
    },
  },
  {
    title: t("name"),
    key: "name",
    align: "center",
    render: (row) => {
      const fullName = [row.firstName, row.lastName].filter(Boolean).join(" ");
      return <div >{fullName || "-"}</div>;
    },
  },
  {
    title: t("group"),
    key: "groupName",
    align: "center",
    render: (row) => {
      return <div class="text-sm">{row.group_name || "-"}</div>;
    },
  },
  {
    title: t("role"),
    key: "role",
    align: "center",
    render: (row) => {
      const roleType = row.role === "admin" ? "error" : "default";
      const roleLabel = row.role === "admin" ? t("admin") : t("member");
      return row.role ? <n-tag type={roleType}>{roleLabel}</n-tag> : "-";
    },
  },
  {
    title: t("joineddate"),
    key: "joinedAt",
    align: "center",
    render: (row) => {
      return row.joinedAt ? (
        <div class="text-sm">
          {moment(row.joinedAt)
            .tz("Asia/Bangkok")
            .format("DD/MM/YYYY HH:mm:ss")}
        </div>
      ) : (
        "-"
      );
    },
  },
  {
    title: t("lastseendate"),
    key: "lastSeenAt",
    align: "center",
    render: (row) => {
      return row.lastSeenAt ? (
        <div class="text-sm">
          {moment(row.lastSeenAt)
            .tz("Asia/Bangkok")
            .format("DD/MM/YYYY HH:mm:ss")}
        </div>
      ) : (
        "-"
      );
    },
  },
  {
    title: t("updatedBy"),
    key: "updatedBy",
    align: "center",
    render: (row) => {
      return row.updatedBy ? (
        row.updatedBy
      ) : (
        "-"
      );
    },
  },
  {
    title: t("manage"),
    align: "center",
    key: "actions",
    render: (row) => {
      return (
        <n-space justify="center">
          <n-button
            type="primary"
            size="small"
            onClick={() => handleEditUser(row)}
          >
            <div class="flex items-center gap-1">
              <icon-park-outline-edit /> {t("edit")}
            </div>
          </n-button>
          <n-button
            type="error"
            size="small"
            onClick={() => handleDeleteUser(row)}
          >
            <div class="flex items-center gap-1">
              <icon-park-outline-delete /> {t("delete")}
            </div>
          </n-button>
        </n-space>
      );
    },
  },
]);

const handleEditUser = (item) => {
  itemsedit.value = { ...item };
  isEditing.value = true;
  showModal.value = true;
};

const handleDeleteUser = (item) => {
  dialog.warning({
    title: t("confirmdelete"),
    content: t("areyousuredelete"),
    positiveText: t("delete"),
    negativeText: t("cancel"),
    onPositiveClick: () => {
      deleteUser(item);
    },
  });
};

const deleteUser = async (item) => {
  try {
    const obj = {
      ID: item._id,
    };
    const response = await http.post(`v1/member/deleteMember`, obj);
    if (response.data.success) {
      message.success(response.data.mes || t("deletesuccess"));
      getData();
    } else {
      message.error(response.data.mes || t("deletefailed"));
    }
  } catch (error) {
    message.error(t("deletefailed"));
  }
};

const saveUser = async () => {
  try {
    const obj = {
      ID: itemsedit.value._id,
      role: itemsedit.value.role,
    };
    const response = await http.post(`v1/member/updateMember`, obj);


    if (response.data.success) {
      message.success(response.data.mes || t("updatesuccess"));
      showModal.value = false;
      getData();
    } else {
      message.error(response.data.mes || t("updatefailed"));
    }
  } catch (error) {
    message.error(t("updatefailed"));
  }
};

const getData = async () => {
  startLoading();
  try {
    const params = {
      perPage: perPage.value,
      page: Page.value,
      userId: model.value.username,
      groupName: model.value.groupName,
    };
    const response = await http.get("v1/member/getMember", { params });
    list.value = response.data.data || [];
    total.value = response.total || 1;
  } catch (error) {
    console.error("Error fetching users:", error);
    message.error(t("fetchfailed"));
  } finally {
    endLoading();
  }
};

const changePage = (page, size) => {
  perPage.value = size;
  Page.value = page;
  getData();
};

const groupOptions = ref([]);
const getGroupList = async () => {
  try {
    const response = await http.get("v1/group-setting/getGroup");
    if (response.data.status) {
      groupOptions.value = response.data.data || [];
      return;
    }
  } catch (error) {
    message.error(t("fetchfailed"));
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  getData();
  getGroupList();
});
</script>

<template>
  <div>
    <n-card>
      <n-space vertical size="large">
        <!-- Header Section -->
        <n-card>
          <n-space justify="space-between" align="center">
            <n-space align="center">
              <n-avatar color="#1a8a93">
                <icon-park-outline-peoples />
              </n-avatar>
              <div>
                <p class="text-lg font-medium">{{ $t("memberlist") }}</p>
                <n-text depth="3">{{ $t("manageMembersAndRoles") }}</n-text>
              </div>
            </n-space>
          </n-space>
        </n-card>

        <!-- Search Form -->
        <n-card>
          <n-form
            ref="formRef"
            :model="model"
            :label-placement="isMobile ? 'top' : 'left'"
            :show-feedback="false"
          >
            <n-grid cols="1 600:2 1000:4" :x-gap="16" :y-gap="16">
              <n-gi>
                <n-form-item :label="$t('userid')" path="username">
                  <n-input
                    v-model:value="model.username"
                    :placeholder="$t('enteUserId')"
                    clearable
                  >
                    <template #prefix>
                      <icon-park-outline-user />
                    </template>
                  </n-input>
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-form-item :label="$t('group')" path="groupName">
                     <n-select
                    v-model:value="model.groupName"
                    :placeholder="$t('selectGroupName')"
                    clearable
                    :options="groupOptions"
                    label-field="title"
                    value-field="title"
                    filterable
                  >
                  </n-select>
                
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-button type="default" @click="getData" block>
                  <template #icon>
                    <icon-park-outline-search />
                  </template>
                  {{ $t("search") }}
                </n-button>
              </n-gi>
              <n-gi>
                <n-button
                  type="default"
                  @click="model = { username: '', groupName: '' }"
                  block
                  secondary
                >
                  <template #icon>
                    <icon-park-outline-refresh />
                  </template>
                  {{ $t("reset") }}
                </n-button>
              </n-gi>
            </n-grid>
          </n-form>
        </n-card>

        <!-- Data Table -->
        <n-data-table
          :scroll-x="1200"
          :columns="columns"
          :data="list"
          :loading="loading"
        />

        <Pagination :count="total" @change="changePage" class="float-right" />
      </n-space>
    </n-card>

    <!-- Edit User Modal -->
    <n-modal v-model:show="showModal" preset="dialog" :show-icon="false">
      <template #header>
        <n-space align="center">
          <icon-park-outline-edit />
          {{ $t("edit") }}
        </n-space>
      </template>

      <n-space vertical size="large">
        <!-- User Information Display -->
        <n-card>
          <n-space vertical size="medium">
            <n-space align="center">
              <n-avatar size="large" color="#1a8a93">
                <icon-park-outline-user />
              </n-avatar>
              <div>
                <p class="text-lg font-medium">
                  {{ itemsedit.firstName }} {{ itemsedit.lastName }}
                </p>
                <n-text depth="3"
                  >{{ $t("userid") }}: {{ itemsedit.userId }}</n-text
                >
              </div>
            </n-space>

            <n-descriptions :column="2" bordered>
              <n-descriptions-item :label="$t('group')">
                {{ itemsedit.groupName || "-" }}
              </n-descriptions-item>
              <n-descriptions-item :label="$t('joineddate')">
                {{
                  itemsedit.joinedAt
                    ? moment(itemsedit.joinedAt)
                        .tz("Asia/Bangkok")
                        .format("DD/MM/YYYY HH:mm:ss")
                    : "-"
                }}
              </n-descriptions-item>
              <n-descriptions-item :label="$t('lastseendate')">
                {{
                  itemsedit.lastSeenAt
                    ? moment(itemsedit.lastSeenAt)
                        .tz("Asia/Bangkok")
                        .format("DD/MM/YYYY HH:mm:ss")
                    : "-"
                }}
              </n-descriptions-item>
            </n-descriptions>
          </n-space>
        </n-card>

        <!-- Role Edit Form -->
        <n-card>
          <template #header>
            <n-space align="center">
              <icon-park-outline-setting />
              {{ $t("roleSettings") }}
            </n-space>
          </template>

          <n-form :model="itemsedit" label-placement="left">
            <n-form-item :label="$t('role')" path="role">
              <n-select
                v-model:value="itemsedit.role"
                :options="roleOptions"
                :placeholder="$t('selectRole')"
                class="w-full"
              />
            </n-form-item>
          </n-form>
        </n-card>
      </n-space>

      <template #action>
        <n-space justify="end">
          <n-button @click="showModal = false">
            {{ $t("cancel") }}
          </n-button>
          <n-button type="primary" @click="saveUser">
            {{ $t("save") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

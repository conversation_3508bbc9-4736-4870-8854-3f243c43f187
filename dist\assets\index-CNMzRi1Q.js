import{_ as S}from"./delete-CCTWGk6A.js";import{_ as ge,a as be}from"./Pagination.vue_vue_type_script_setup_true_lang-uFMUKmhT.js";import{x as ke,s as I,b as $,e as b,d as he,y as ye,z as xe,A as we,r as d,C as Be,D as Ce,f as e,E as V,j as c,F as Ee,B as j,G as Y,H as E,I as $e,o as Ae,w as t,J as Me,K as Ne,t as i,i as l,L as Ue,M as ze,N as De,O as Ge,P as je,c as T,v as Ie,Q as qe,l as Le,R as Pe}from"./index-DSmp6iCg.js";import{_ as Fe}from"./plus-CxnlDpiD.js";import{_ as Oe}from"./edit-DdCXKTed.js";import{h as Re}from"./moment-zH0z38ay.js";import{_ as He}from"./text-Df11blX7.js";import{_ as Se,a as Ve}from"./FormItem-Be7I1W2B.js";import{_ as Ye,a as Te}from"./Grid-lBi2xqol.js";import{_ as Qe}from"./Input-D61U907N.js";import"./Checkbox-CuJqEfdi.js";import"./Forward-XU0gTivF.js";const Je={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Ke(Q,n){return $(),I("svg",Je,n[0]||(n[0]=[b("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[b("path",{d:"M4 10a2 2 0 0 1 2-2h36a2 2 0 0 1 2 2v28a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2z"}),b("path",{"stroke-linecap":"square",d:"M4 16h40"}),b("path",{"stroke-linecap":"round",d:"M27 32h9m8-22v16M4 10v16"})],-1)]))}const We=ke({name:"icon-park-outline-bank-card",render:Ke}),Xe={class:"text-lg font-medium"},Ze={class:"flex justify-end"},ea={class:"flex items-center gap-2"},_a=he({__name:"index",setup(Q){const{t:n}=ye(),u=xe(),J=we(),K=a=>Re(a).format("YYYY-MM-DD HH:mm:ss"),f=d({bank:null,active:null}),W=Be("(max-width: 768px)"),k=d(!1),s=d([]),x=d(!1),q=d(1),z=d(10),D=d(1),A=d(!1),L=d([]),M=d([]),B=d([]),X=async a=>{try{const o=await E.get("v1/bank/getBankList",{params:{bank:a}});B.value=o.data.data}catch(o){u.error(n("fetchfailed")),console.error("Error fetching bank list:",o)}},Z=Ce(()=>[{title:n("no."),key:"index",align:"center",render:(a,o)=>z.value*(D.value-1)+o+1},{title:n("group"),key:"group_name",align:"center",render:a=>e("div",{class:"text-center"},[e(V,{type:"default",size:"medium",class:"mb-1"},{default:()=>[a.group_name]}),e("div",{class:"text-xs"},[a.data?.length||0,c(" "),n("bank")])])},{title:n("bank"),key:"bank",align:"center",render:a=>e("div",{class:"space-y-2"},[a.data?.map((o,_)=>{const m=B.value.find(w=>w.value===o.bank);return e("div",{key:_,class:"flex items-center justify-between p-3  rounded-md border border-gray-200 dark:border-gray-500/20"},[e("div",{class:"flex items-center gap-3"},[e("img",{src:`/images/bankIcon/${o.path_photo}`,class:"w-8 h-8 rounded"},null),e("div",null,[e("div",{class:"font-medium text-sm text-start"},[m?.label]),e("div",{class:"text-xs  font-mono"},[o.accno,c(" - "),o.name])])]),e(V,{type:o.active?"success":"error",size:"small"},{default:()=>[o.active?n("active"):n("inactive")]})])}),(!a.data||a.data.length===0)&&e("div",{class:"text-center text-gray-400 py-4"},[e(Ee,{description:n("nobankAdded"),size:"small"},null)])])},{title:n("updatedAt"),key:"updatedAt",align:"center",render:a=>e("div",null,[K(a.updatedAt)])},{title:n("updatedBy"),key:"updatedBy",align:"center",render:a=>e("div",null,[a.updatedBy||"-"])},{title:n("manage"),align:"center",key:"actions",render:a=>e(Y,{justify:"center",size:"small"},{default:()=>[e(j,{type:"primary",size:"small",onClick:()=>ae(a)},{default:()=>[e("div",{class:"flex items-center gap-1"},[e(Oe,null,null),n("edit")])]}),e(j,{type:"error",size:"small",onClick:()=>le(a)},{default:()=>[e("div",{class:"flex items-center gap-1"},[e(S,null,null),n("delete")," "])]})]})}]),ee=()=>{s.value={group_name:null,bank:[{type:"bank",bank:null,accno:"",name:"",active:!0,preferred:!0,path_photo:null}]},x.value=!1,k.value=!0},ae=a=>{console.log(a),s.value={_id:a._id,group_name:a.group_name,scope:a.scope,bank:a.data},x.value=!0,k.value=!0},ne=()=>{s.value.bank||(s.value.bank=[]),s.value.bank.push({type:"bank",bank:null,accno:"",name:"",active:!0,preferred:!0,path_photo:null})},te=a=>{s.value.bank&&s.value.bank.length>1&&s.value.bank.splice(a,1)},le=a=>{J.warning({title:n("confirmdelete"),content:n("areyousure"),positiveText:n("confirm"),negativeText:n("cancel"),onPositiveClick:()=>{oe(a)}})},oe=async a=>{try{const o=await E.post("v1/bank/deleteBank",{ID:a._id});o.data.success?(u.success(o.data.mes),C()):u.error(o.data.mes)}catch{u.error(n("deletefailed"))}},se=async()=>{if(s.value){if(!s.value.group_name?.trim()){u.error(n("pleaseEnterGroupName"));return}if(!s.value.bank||s.value.bank.length===0){u.error(n("pleaseAddAtLeastOneBank"));return}for(let a=0;a<s.value.bank.length;a++){const o=s.value.bank[a];if(!o.bank){u.error(n("pleaseSelectBankForEntry")+` ${a+1}`);return}if(!o.accno?.trim()){u.error(n("pleaseEnterAccountNumberForEntry")+` ${a+1}`);return}if(!o.name?.trim()){u.error(n("pleaseEnterAccountNameForEntry")+` ${a+1}`);return}}try{const a=M.value.find(w=>w._id===s.value.group_name);console.log(a);const o={group_name:a?.title||s.value.group_name,data:s.value.bank,scope:a?.chatId||s.value.scope};x.value&&(o.ID=s.value._id);const _=(x.value,"v1/bank/storeBank"),m=await E.post(_,o);m.data.success===!0?(u.success(m.data.mes),k.value=!1,C()):u.error(m.data.mes)}catch{u.error(n("savefailed"))}}};$e(()=>s.value.bank,a=>{a&&a.forEach(o=>{if(o.bank&&!o.path_photo){const _=B.value.find(m=>m.bankcode===o.bank);_&&(o.path_photo=_.path_photo)}})},{deep:!0});const C=async()=>{try{A.value=!0;const a={page:D.value,limit:z.value,group_name:f.value.group_name,bank_name:f.value.bank},o=await E.get("v1/bank/getBank",{params:a});L.value=o.data.data||[],q.value=o.data.total||1}catch{u.error(n("fetchfailed"))}finally{A.value=!1}},re=async()=>{try{const a=await E.get("v1/group-setting/getGroup");if(a.data.status){M.value=a.data.data||[];return}}catch{u.error(n("fetchfailed"))}finally{A.value=!1}},ue=(a,o)=>{z.value=o,D.value=a,C()},P=a=>e("div",{class:"flex items-center gap-2"},[e("img",{src:`/images/bankIcon/${a.path_photo}`,alt:a.name,class:"w-5 h-5 rounded"},null),e("span",null,[a.name])]);Ae(()=>{C(),re(),X()});const ce=()=>{f.value={group_name:null,bank:null}};return(a,o)=>{const _=We,m=Ne,w=He,h=Y,F=Fe,y=j,N=Me,U=Ue,p=Ve,v=Te,ie=ze,de=De,O=Ye,R=Se,pe=ge,_e=be,me=S,fe=qe,H=Qe,ve=Pe;return $(),I("div",null,[e(N,null,{default:t(()=>[e(h,{vertical:"",size:"large"},{default:t(()=>[e(N,null,{default:t(()=>[e(h,{justify:"space-between",align:"center"},{default:t(()=>[e(h,{align:"center"},{default:t(()=>[e(m,{color:"#1a8a93"},{default:t(()=>[e(_)]),_:1}),b("div",null,[b("p",Xe,i(a.$t("banklist")),1),e(w,{depth:"3"},{default:t(()=>[c(i(a.$t("manageBankAccounts")),1)]),_:1})])]),_:1}),e(y,{type:"primary",onClick:ee},{icon:t(()=>[e(F)]),default:t(()=>[c(" "+i(l(n)("addbank")),1)]),_:1})]),_:1})]),_:1}),e(N,null,{default:t(()=>[e(R,{ref:"formRef",model:l(f),"label-placement":l(W)?"top":"left","show-feedback":!1},{default:t(()=>[e(O,{cols:"1 600:2 1000:4","x-gap":16,"y-gap":16},{default:t(()=>[e(v,null,{default:t(()=>[e(p,{label:l(n)("group"),path:"group_name"},{default:t(()=>[e(U,{value:l(f).group_name,"onUpdate:value":o[0]||(o[0]=r=>l(f).group_name=r),options:l(M),placeholder:l(n)("selectGroupName"),filterable:"",clearable:"","label-field":"title","value-field":"chatId"},null,8,["value","options","placeholder"])]),_:1},8,["label"])]),_:1}),e(v,null,{default:t(()=>[e(p,{label:l(n)("bank"),path:"bank"},{default:t(()=>[e(U,{value:l(f).bank,"onUpdate:value":o[1]||(o[1]=r=>l(f).bank=r),options:l(B),placeholder:l(n)("selectBank"),"render-label":P,filterable:"",clearable:""},null,8,["value","options","placeholder"])]),_:1},8,["label"])]),_:1}),e(v,null,{default:t(()=>[e(p,{label:" ",path:"search"},{default:t(()=>[e(y,{type:"default",onClick:C,block:""},{icon:t(()=>[e(ie)]),default:t(()=>[c(" "+i(l(n)("search")),1)]),_:1})]),_:1})]),_:1}),e(v,null,{default:t(()=>[e(p,{label:" ",path:"refresh"},{default:t(()=>[e(y,{type:"default",onClick:ce,block:"",secondary:""},{icon:t(()=>[e(de)]),default:t(()=>[c(" "+i(l(n)("reset")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model","label-placement"])]),_:1}),e(pe,{"scroll-x":1200,columns:l(Z),data:l(L),loading:l(A)},null,8,["columns","data","loading"]),b("div",Ze,[e(_e,{count:l(q),onChange:ue},null,8,["count"])])]),_:1})]),_:1}),e(ve,{show:l(k),"onUpdate:show":o[4]||(o[4]=r=>Le(k)?k.value=r:null),preset:"dialog","show-icon":!1,style:{width:"800px"}},{header:t(()=>[e(h,{align:"center"},{default:t(()=>[b("div",ea,[e(_),c(" "+i(l(x)?l(n)("editbank"):l(n)("addbank")),1)])]),_:1})]),action:t(()=>[e(h,{justify:"end"},{default:t(()=>[e(y,{onClick:o[3]||(o[3]=r=>k.value=!1)},{default:t(()=>[c(i(l(n)("cancel")),1)]),_:1}),e(y,{type:"primary",onClick:se},{default:t(()=>[c(i(l(n)("save")),1)]),_:1})]),_:1})]),default:t(()=>[e(R,{"label-placement":"top",class:"mt-5"},{default:t(()=>[e(p,{label:l(n)("group"),required:""},{default:t(()=>[e(U,{value:l(s).group_name,"onUpdate:value":o[2]||(o[2]=r=>l(s).group_name=r),options:l(M),placeholder:l(n)("selectGroupName"),filterable:"",clearable:"","label-field":"title","value-field":"_id",disabled:l(x)},null,8,["value","options","placeholder","disabled"])]),_:1},8,["label"]),e(p,{label:l(n)("bank"),required:""},{default:t(()=>[e(h,{vertical:"",size:"large",class:"w-full"},{default:t(()=>[($(!0),I(Ge,null,je(l(s).bank,(r,G)=>($(),T(N,{key:G},{default:t(()=>[e(h,{justify:"space-between",align:"center",class:"mb-3"},{default:t(()=>[e(w,{strong:""},{default:t(()=>[c(i(l(n)("bank"))+" "+i(G+1),1)]),_:2},1024),l(s).bank&&l(s).bank.length>1?($(),T(y,{key:0,type:"error",size:"small",onClick:g=>te(G)},{icon:t(()=>[e(me)]),default:t(()=>[c(" "+i(l(n)("delete")),1)]),_:2},1032,["onClick"])):Ie("",!0)]),_:2},1024),e(O,{cols:"1 s:2","x-gap":12,"y-gap":12,responsive:"screen"},{default:t(()=>[e(v,null,{default:t(()=>[e(p,{label:l(n)("bankName"),required:""},{default:t(()=>[e(U,{value:r.bank,"onUpdate:value":g=>r.bank=g,options:l(B),placeholder:l(n)("selectBank"),"render-label":P,"value-field":"bankcode","label-field":"name",clearable:"",filterable:""},null,8,["value","onUpdate:value","options","placeholder"])]),_:2},1032,["label"])]),_:2},1024),e(v,null,{default:t(()=>[e(p,{label:l(n)("status"),required:""},{default:t(()=>[e(fe,{value:r.active,"onUpdate:value":g=>r.active=g,"checked-value":!0,"unchecked-value":!1},{checked:t(()=>[c(i(l(n)("active")),1)]),unchecked:t(()=>[c(i(l(n)("inactive")),1)]),_:2},1032,["value","onUpdate:value"])]),_:2},1032,["label"])]),_:2},1024),e(v,null,{default:t(()=>[e(p,{label:l(n)("accountnumber"),required:""},{default:t(()=>[e(H,{type:"number",class:"w-full","show-button":!1,value:r.accno,"onUpdate:value":g=>r.accno=g,placeholder:l(n)("enterAccountNumber")},null,8,["value","onUpdate:value","placeholder"])]),_:2},1032,["label"])]),_:2},1024),e(v,null,{default:t(()=>[e(p,{label:l(n)("accountname"),required:""},{default:t(()=>[e(H,{value:r.name,"onUpdate:value":g=>r.name=g,placeholder:l(n)("enterAccountName")},null,8,["value","onUpdate:value","placeholder"])]),_:2},1032,["label"])]),_:2},1024)]),_:2},1024)]),_:2},1024))),128)),e(y,{type:"default",dashed:"",block:"",onClick:ne,class:"mt-5"},{icon:t(()=>[e(F)]),default:t(()=>[c(" "+i(l(n)("addbank")),1)]),_:1})]),_:1})]),_:1},8,["label"])]),_:1})]),_:1},8,["show"])])}}});export{_a as default};

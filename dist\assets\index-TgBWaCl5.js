import{_ as z}from"./edit-DdCXKTed.js";import{_ as re,a as ue}from"./Pagination.vue_vue_type_script_setup_true_lang-uFMUKmhT.js";import{d as de,y as ie,z as pe,A as me,C as ce,r as m,D as _e,f as e,E as fe,B as E,j as i,G as A,H as k,o as ve,s as ge,b as we,w as n,J as ye,e as H,K as he,t as p,i as t,fe as be,M as ke,N as $e,l as N,R as Pe}from"./index-DSmp6iCg.js";import{_ as Ue}from"./plus-CxnlDpiD.js";import{_ as Ce}from"./peoples-Doj-bBmD.js";import{_ as Me}from"./delete-CCTWGk6A.js";import{m as q}from"./index-BqRpacQ4.js";import{u as Ee}from"./useBoolean-BE5LkQbP.js";import{_ as De}from"./text-Df11blX7.js";import{_ as je,a as Be}from"./FormItem-Be7I1W2B.js";import{_ as Ye,a as ze}from"./Grid-lBi2xqol.js";import{_ as Ae}from"./Input-D61U907N.js";import"./Checkbox-CuJqEfdi.js";import"./Forward-XU0gTivF.js";import"./moment-zH0z38ay.js";const He={class:"text-lg font-medium"},Ze=de({__name:"index",setup(Ne){const{t:s}=ie(),u=pe(),F=me(),T=ce("(max-width: 768px)"),{bool:R,setTrue:V,setFalse:L}=Ee(!1),h=m({username:""}),g=m(!1),w=m(!1),r=m({id:null,username:"",password:"",confirmPassword:""}),d=m({username:"",password:"",confirmPassword:""}),S=m(!1),D=m(1),$=m(10),P=m(1),j=m([]),G=_e(()=>[{title:s("no."),align:"center",key:"index",render:(a,o)=>$.value*(P.value-1)+o+1},{title:s("username"),key:"username",align:"center",render:a=>e("div",{class:"text-sm font-mono"},[a.username||"-"])},{title:s("position"),key:"position_type",align:"center",render:a=>e(fe,{type:a.position_type==1?"error":"info"},{default:()=>[a.position_type==1?s("admin"):s("member")]})},{title:s("lastlogin"),key:"last_login",align:"center",render:a=>a.last_login?e("div",{class:"text-sm"},[q(a.last_login).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss")]):"-"},{title:s("lastip"),key:"last_ip",align:"center",render:a=>e("div",{class:"text-sm font-mono"},[a.last_ip||"-"])},{title:s("createdate"),key:"created_at",align:"center",render:a=>a.created_at?e("div",{class:"text-sm"},[q(a.created_at).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss")]):"-"},{title:s("manage"),align:"center",key:"actions",render:a=>e(A,{justify:"center"},{default:()=>[e(E,{type:"primary",size:"small",onClick:()=>J(a)},{default:()=>[e("div",{class:"flex items-center gap-1"},[e(z,null,null),i(" "),s("edit")])]}),e(E,{type:"error",size:"small",onClick:()=>K(a)},{default:()=>[e("div",{class:"flex items-center gap-1"},[e(Me,null,null),i(" "),s("delete")])]})]})}]),I=()=>{d.value={username:"",password:"",confirmPassword:""},w.value=!0},J=a=>{r.value={...a,password:"",confirmPassword:""},S.value=!0,g.value=!0},K=a=>{F.warning({title:s("confirmdelete"),content:s("areyousuredelete"),positiveText:s("delete"),negativeText:s("cancel"),onPositiveClick:()=>{Q(a)}})},Q=async a=>{try{const o={id:a.id},b=await k.post("v1/employee/deleteEmployee",o);b.data.success?(u.success(b.data.mes||s("deletesuccess")),y()):u.error(b.data.mes||s("deletefailed"))}catch{u.error(s("deletefailed"))}},O=async()=>{if(r.value.password&&r.value.password!==r.value.confirmPassword){u.error(s("validation.passwordMismatch"));return}try{const a={id:r.value.id,username:r.value.username,password:r.value.password},o=await k.post("v1/employee/storeEmployee",a);o.data.success?(u.success(o.data.mes||s("updatesuccess")),g.value=!1,y()):u.error(o.data.mes||s("updatefailed"))}catch{u.error(s("updatefailed"))}},W=async()=>{if(d.value.password!==d.value.confirmPassword){u.error(s("validation.passwordMismatch"));return}try{const a={username:d.value.username,password:d.value.password},o=await k.post("v1/employee/storeEmployee",a);o.data.success?(u.success(o.data.mes||s("addSuccess")),w.value=!1,y()):u.error(o.data.mes||s("addFailed"))}catch{u.error(s("addFailed"))}},y=async()=>{V();try{const a={perPage:$.value,page:P.value,username:h.value.username},o=await k.get("v1/employee/getEmployee",{params:a});j.value=o.data.data||[],D.value=o.data.total||1}catch(a){console.error("Error fetching employees:",a),u.error(s("fetchfailed"))}finally{L()}},X=(a,o)=>{$.value=o,P.value=a,y()};return ve(()=>{y()}),(a,o)=>{const b=Ce,Z=he,x=De,c=A,B=Ue,_=E,U=ye,ee=be,f=Ae,v=Be,C=ze,ae=ke,ne=$e,oe=Ye,M=je,se=re,te=ue,Y=Pe,le=z;return we(),ge("div",null,[e(U,null,{default:n(()=>[e(c,{vertical:"",size:"large"},{default:n(()=>[e(U,null,{default:n(()=>[e(c,{justify:"space-between",align:"center"},{default:n(()=>[e(c,{align:"center"},{default:n(()=>[e(Z,{color:"#1a8a93"},{default:n(()=>[e(b)]),_:1}),H("div",null,[H("p",He,p(a.$t("employeelist")),1),e(x,{depth:"3"},{default:n(()=>[i(p(a.$t("manageEmployees")),1)]),_:1})])]),_:1}),e(_,{type:"primary",onClick:I},{icon:n(()=>[e(B)]),default:n(()=>[i(" "+p(t(s)("addemployee")),1)]),_:1})]),_:1})]),_:1}),e(U,null,{default:n(()=>[e(M,{ref:"formRef",model:t(h),"label-placement":t(T)?"top":"left","show-feedback":!1},{default:n(()=>[e(oe,{cols:"1 600:2 1000:4","x-gap":16,"y-gap":16},{default:n(()=>[e(C,null,{default:n(()=>[e(v,{label:a.$t("username"),path:"username"},{default:n(()=>[e(f,{value:t(h).username,"onUpdate:value":o[0]||(o[0]=l=>t(h).username=l),placeholder:a.$t("enterUsername"),clearable:""},{prefix:n(()=>[e(ee)]),_:1},8,["value","placeholder"])]),_:1},8,["label"])]),_:1}),e(C,null,{default:n(()=>[e(_,{type:"default",onClick:y,block:""},{icon:n(()=>[e(ae)]),default:n(()=>[i(" "+p(a.$t("search")),1)]),_:1})]),_:1}),e(C,null,{default:n(()=>[e(_,{type:"default",onClick:o[1]||(o[1]=l=>h.value={username:""}),block:"",secondary:""},{icon:n(()=>[e(ne)]),default:n(()=>[i(" "+p(a.$t("reset")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["model","label-placement"])]),_:1}),e(se,{"scroll-x":1200,columns:t(G),data:t(j),loading:t(R)},null,8,["columns","data","loading"]),e(te,{count:t(D),onChange:X,class:"float-right"},null,8,["count"])]),_:1})]),_:1}),e(Y,{show:t(w),"onUpdate:show":o[6]||(o[6]=l=>N(w)?w.value=l:null),preset:"dialog","show-icon":!1},{header:n(()=>[e(c,{align:"center"},{default:n(()=>[e(B),i(" "+p(a.$t("addemployee")),1)]),_:1})]),action:n(()=>[e(c,{justify:"end"},{default:n(()=>[e(_,{onClick:o[5]||(o[5]=l=>w.value=!1)},{default:n(()=>[i(p(a.$t("cancel")),1)]),_:1}),e(_,{type:"primary",onClick:W},{default:n(()=>[i(p(a.$t("add")),1)]),_:1})]),_:1})]),default:n(()=>[e(M,{model:t(d),"label-placement":"left",class:"mt-10"},{default:n(()=>[e(v,{label:a.$t("username"),path:"username",required:""},{default:n(()=>[e(f,{value:t(d).username,"onUpdate:value":o[2]||(o[2]=l=>t(d).username=l),placeholder:a.$t("enterUsername")},null,8,["value","placeholder"])]),_:1},8,["label"]),e(v,{label:a.$t("password"),path:"password",required:""},{default:n(()=>[e(f,{value:t(d).password,"onUpdate:value":o[3]||(o[3]=l=>t(d).password=l),type:"password",placeholder:a.$t("enterPassword")},null,8,["value","placeholder"])]),_:1},8,["label"]),e(v,{label:a.$t("confirmpassword"),path:"confirmPassword",required:""},{default:n(()=>[e(f,{value:t(d).confirmPassword,"onUpdate:value":o[4]||(o[4]=l=>t(d).confirmPassword=l),type:"password",placeholder:a.$t("confirmpassword")},null,8,["value","placeholder"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["show"]),e(Y,{show:t(g),"onUpdate:show":o[11]||(o[11]=l=>N(g)?g.value=l:null),preset:"dialog","show-icon":!1},{header:n(()=>[e(c,{align:"center"},{default:n(()=>[e(le),i(" "+p(a.$t("edit")),1)]),_:1})]),action:n(()=>[e(c,{justify:"end"},{default:n(()=>[e(_,{onClick:o[10]||(o[10]=l=>g.value=!1)},{default:n(()=>[i(p(a.$t("cancel")),1)]),_:1}),e(_,{type:"primary",onClick:O},{default:n(()=>[i(p(a.$t("save")),1)]),_:1})]),_:1})]),default:n(()=>[e(M,{model:t(r),"label-placement":"left"},{default:n(()=>[e(v,{label:a.$t("username"),path:"username",required:"",class:"mt-10"},{default:n(()=>[e(f,{value:t(r).username,"onUpdate:value":o[7]||(o[7]=l=>t(r).username=l),placeholder:a.$t("enterUsername")},null,8,["value","placeholder"])]),_:1},8,["label"]),e(v,{label:a.$t("newpassword"),path:"password"},{default:n(()=>[e(f,{value:t(r).password,"onUpdate:value":o[8]||(o[8]=l=>t(r).password=l),type:"password",placeholder:a.$t("enterNewPassword")},null,8,["value","placeholder"])]),_:1},8,["label"]),e(v,{label:a.$t("confirmpassword"),path:"confirmPassword"},{default:n(()=>[e(f,{value:t(r).confirmPassword,"onUpdate:value":o[9]||(o[9]=l=>t(r).confirmPassword=l),type:"password",placeholder:a.$t("confirmpassword")},null,8,["value","placeholder"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["show"])])}}});export{Ze as default};

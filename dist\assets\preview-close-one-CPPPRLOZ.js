import{x as o,s as t,b as r,e as n}from"./index-DSmp6iCg.js";const s={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function l(i,e){return r(),t("svg",s,e[0]||(e[0]=[n("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[n("path",{d:"M24 36c11.046 0 20-12 20-12s-8.954-12-20-12S4 24 4 24s8.954 12 20 12Z"}),n("path",{d:"M24 29a5 5 0 1 0 0-10a5 5 0 0 0 0 10Z"})],-1)]))}const p=o({name:"icon-park-outline-preview-open",render:l}),a={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function c(i,e){return r(),t("svg",a,e[0]||(e[0]=[n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4",d:"M9.858 18C6.238 21 4 24 4 24s8.954 12 20 12c1.37 0 2.708-.185 4-.508M20.032 12.5c1.282-.318 2.61-.5 3.968-.5c11.046 0 20 12 20 12s-2.239 3-5.858 6m-17.828-9.379a5 5 0 0 0 7.186 6.95M42 42L6 6"},null,-1)]))}const u=o({name:"icon-park-outline-preview-close-one",render:c});export{p as _,u as a};

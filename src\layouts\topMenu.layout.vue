<script lang="ts" setup>
import moment from "moment";
import { useAppStore, useRouteStore } from "@/store";
import type { DrawerPlacement } from "naive-ui";
import http from "@/service/axios";
import {
  BackTop,
  FullScreen,
  Logo,
  Menu,
  Notices,
  Search,
  TabBar,
  Setting,
  UserCenter,
} from "./components";
const { t } = useI18n();
const routeStore = useRouteStore();
const appStore = useAppStore();
const menu = ref(false);
const placement = ref<DrawerPlacement>("right");
const activate = (place: DrawerPlacement) => {
  menu.value = true;
  placement.value = place;
};
const checkScreenSize = () => {
  if (window.innerWidth > 768) {
    menu.value = false;
  }
};
const route = useRoute();
watch(
  () => route.path,
  () => {
    menu.value = false;
  }
);
onMounted(() => {
  // checkScreenSize();
  window.addEventListener("resize", checkScreenSize);
});

onUnmounted(() => {
  window.removeEventListener("resize", checkScreenSize);
});
</script>

<template>
  <n-layout class="wh-full" embedded>
    <n-layout
      class="h-full flex flex-col"
      content-style="display: flex;flex-direction: column;min-height:100%;"
      embedded
      :native-scrollbar="false"
    >
      <n-layout-header
        bordered
        position="absolute"
        class="z-999 hidden sm:block"
      >
        <div
          v-if="!appStore.contentFullScreen"
          class="h-60px flex-y-center justify-between shrink-0 ml-3"
        >
          <Logo v-if="appStore.showLogo" />
          <Menu mode="horizontal" responsive />

          <div class="flex-y-center gap-1 h-full p-x-xl">
            <div
              class="hidden lg:flex justify-center items-center gap-2 text-nowrap mr-10"
            ></div>
            <!-- <Search />
            <Notices /> -->
            <FullScreen />
            <DarkModeSwitch />
            <Setting />
            <LangsSwitch />

            <div class="flex items-center">
              <UserCenter />
            </div>
          </div>
        </div>
        <TabBar v-if="appStore.showTabs" class="h-45px" />
      </n-layout-header>

      <n-layout-header
        bordered
        position="absolute"
        class="z-999 block sm:hidden"
      >
        <div
          v-if="!appStore.contentFullScreen"
          class="h-60px flex-y-center justify-between"
        >
          <div class="flex-y-center h-full">
            <n-button
              secondary
              type="primary"
              size="small"
              @click="activate('left')"
              class="ml-4"
            >
              <icon-park-outline-application-menu />
            </n-button>
            <n-drawer
              v-model:show="menu"
              :width="appStore.collapsed === true ? '30%' : '70%'"
              :placement="placement"
              :class="
                appStore.storeColorMode === 'light'
                  ? 'bg-[#ffffff]'
                  : 'bg-[#18181c]'
              "
            >
              <n-drawer-content>
                <div class="text-xl cursor-pointer gap-2">
                  <Logo v-if="appStore.showLogo" />
                  <n-scrollbar>
                    <Menu />
                  </n-scrollbar>
                </div>
              </n-drawer-content>
            </n-drawer>
            <!-- <CollapaseButton /> -->
            <!-- <Breadcrumb /> -->
          </div>
          <div class="flex-y-center gap-1 h-full p-x-xs">
            <!-- <Search /> -->

            <!-- <FullScreen /> -->
            <DarkModeSwitch />
            <!-- <Notices /> -->
            <!-- <Setting /> -->
            <LangsSwitch />

            <UserCenter />
          </div>
        </div>
      </n-layout-header>

      <div
        class="flex-1 p-16px flex flex-col"
        :class="{
          'p-t-121px': appStore.showTabs,
          'p-b-56px': appStore.showFooter && !appStore.contentFullScreen,
          'p-t-76px': !appStore.showTabs,
          'p-t-61px': appStore.contentFullScreen,
        }"
      >
        <router-view v-slot="{ Component, route }" class="flex-1">
          <transition :name="appStore.transitionAnimation" mode="out-in">
            <keep-alive :include="routeStore.cacheRoutes">
              <component
                :is="Component"
                v-if="appStore.loadFlag"
                :key="route.fullPath"
              />
            </keep-alive>
          </transition>
        </router-view>
      </div>
      <n-layout-footer
        v-if="appStore.showFooter && !appStore.contentFullScreen"
        bordered
        position="absolute"
        class="h-40px flex-center"
      >
        <!-- {{ appStore.footerText }} -->
        {{ $t("copyright") }} {{ appStore.footerText }}
      </n-layout-footer>
      <BackTop />
    </n-layout>
  </n-layout>
</template>

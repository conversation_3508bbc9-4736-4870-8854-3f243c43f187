import{d as M,u as k,r as C,o as b,a as P,c as g,b as x,w as r,e as t,f as i,_ as S,g as $,h as z,i as a,j as T,t as B,T as E,k as N,l as V,n as I,m as L}from"./index-DSmp6iCg.js";import{_ as A}from"./index.vue_vue_type_script_setup_true_lang-CX2tiFny.js";import{_ as D}from"./index.vue_vue_type_script_setup_true_lang-BueESKK2.js";import{_ as F}from"./index.vue_vue_type_script_setup_true_lang-DNDdZs01.js";import{N as H}from"./headers-DsTJCVw-.js";import"./preview-close-one-CPPPRLOZ.js";import"./lock-CFXZdX3i.js";import"./FormItem-Be7I1W2B.js";import"./Checkbox-CuJqEfdi.js";import"./Input-D61U907N.js";import"./Grid-lBi2xqol.js";import"./text-Df11blX7.js";const R={class:"fixed top-15px sm:top-40px right-10px sm:right-40px text-lg z-100 rounded-xl",style:{"background-color":"var(--body-color)"}},U={class:"flex flex-col items-end"},W={class:"w-full flex flex-col items-center"},j=["src"],le=M({__name:"index",setup(q){const h=k(),l=C("login"),_={login:A,register:D,resetPwd:F},w="BBPAY BACK OFFICE";return b(()=>{const u=window.innerWidth<=768,o=document.getElementById("particles-canvas"),n=o.getContext("2d"),s=()=>{o.width=o.offsetWidth,o.height=o.offsetHeight};s(),window.addEventListener("resize",s);const d=[],c=u?30:100;for(let e=0;e<c;e++)d.push({x:Math.random()*o.width,y:Math.random()*o.height,size:Math.random()*2,opacity:Math.random()*.5+.3,twinkleSpeed:Math.random()*.02,twinklePhase:Math.random()*Math.PI*2,vx:(Math.random()-.5)*.2,vy:(Math.random()-.5)*.2});function m(){n.clearRect(0,0,o.width,o.height),d.forEach((e,K)=>{if(e.isShooting)e.x+=Math.cos(e.angle)*e.speed,e.y+=Math.sin(e.angle)*e.speed,e.tail.unshift({x:e.x,y:e.y}),e.tail.length>e.maxTailLength&&e.tail.pop(),n.beginPath(),n.moveTo(e.x,e.y),e.tail.forEach((p,f)=>{const y=1-f/e.maxTailLength,v=h.colorMode==="dark"?"255, 255, 255":"0, 0, 0";n.strokeStyle=`rgba(${v}, ${y})`,n.lineTo(p.x,p.y)}),n.stroke(),(e.y>o.height||e.x>o.width)&&(e.x=Math.random()*o.width,e.y=0,e.tail=[],e.angle=(Math.random()*10+10)*(Math.PI/180));else{e.x+=e.vx,e.y+=e.vy,e.x<0&&(e.x=o.width),e.x>o.width&&(e.x=0),e.y<0&&(e.y=o.height),e.y>o.height&&(e.y=0),e.twinklePhase+=e.twinkleSpeed;const p=Math.sin(e.twinklePhase)*.3+.7;n.beginPath(),n.arc(e.x,e.y,e.size,0,Math.PI*2);const f=h.colorMode==="dark"?"255, 255, 255":"0, 0, 0";n.fillStyle=`rgba(${f}, ${e.opacity*p})`,n.fill()}}),requestAnimationFrame(m)}m(),P(()=>{window.removeEventListener("resize",s)})}),(u,o)=>{const n=S,s=$,d=H,c=L;return x(),g(c,{class:"wh-full flex-center",style:{"background-color":"var(--body-color)"}},{default:r(()=>[o[1]||(o[1]=t("canvas",{id:"particles-canvas",class:"fixed inset-0 w-full h-full pointer-events-none"},null,-1)),t("div",R,[t("div",U,[i(n),i(s),i(z)])]),i(c,{class:I(["py-lg sm:p-4xl h-full w-full z-10 sm:rounded-12px sm:bg-white  sm:dark:bg-[#18181c] sm:shadow-md duration-300 ",a(l)==="register"?"sm:w-1440px sm:h-850px sm:flex-center":"sm:w-500px  sm:h-700px flex-center"])},{default:r(()=>[t("div",W,[t("img",{src:(a(h).storeColorMode==="light","/images/logo/logo.png"),class:"w-150px h-auto rounded-full",alt:"logo"},null,8,j),i(d,null,{default:r(()=>[T(B(a(w)),1)]),_:1}),i(E,{name:"fade-slide",mode:"out-in"},{default:r(()=>[(x(),g(N(_[a(l)]),{modelValue:a(l),"onUpdate:modelValue":o[0]||(o[0]=m=>V(l)?l.value=m:null),class:"w-85%"},null,8,["modelValue"]))]),_:1})])]),_:1},8,["class"]),o[2]||(o[2]=t("div",null,null,-1))]),_:1})}}});export{le as default};

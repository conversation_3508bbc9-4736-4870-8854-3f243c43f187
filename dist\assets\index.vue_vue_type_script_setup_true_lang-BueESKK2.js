import{_ as F,a as K}from"./preview-close-one-CPPPRLOZ.js";import{d as Q,fk as W,r as i,z as X,y as Y,o as Z,H as _,s as ee,b as ne,f as e,w as l,j as c,t as m,i as a,e as le,J as te,L as ae,G as oe,l as re,B as ie,fo as ue}from"./index-DSmp6iCg.js";import{_ as se,a as pe}from"./Grid-lBi2xqol.js";import{_ as de}from"./text-Df11blX7.js";import{a as _e}from"./headers-DsTJCVw-.js";import{_ as ce,a as me}from"./FormItem-Be7I1W2B.js";import{_ as ge}from"./Input-D61U907N.js";import{_ as fe}from"./Checkbox-CuJqEfdi.js";const ve={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},$e=Q({__name:"index",emits:["update:modelValue"],setup(be,{emit:T}){const V=W(),U=i(null),w=X(),v=i(!1),A=T;function B(){A("update:modelValue","login")}const{t:k}=Y(),G=i({phone:{required:!0,trigger:"input",validator:(o,t)=>t?/^\d+$/.test(t)?/^[0-9]{10}$/.test(t)?!0:new Error("เบอร์โทรต้องยาว 10 ตัวเท่านั้น"):new Error("เบอร์โทรต้องเป็นตัวเลขเท่านั้น"):new Error(k("login.telRuleTip"))},pwd:{required:!0,trigger:"input",validator:(o,t)=>t?t.length<6?new Error("รหัสผ่านต้องยาวอย่างน้อย 6 ตัวอักษร"):!0:new Error(k("login.passwordRuleTip"))},rePwd:{required:!0,trigger:"input",validator:(o,t)=>t?t!==n.value.pwd?new Error("รหัสผ่านไม่ตรงกัน"):!0:new Error(k("login.checkPasswordRuleTip"))},rank:{type:"number",required:!0,trigger:["input","change"],message:"กรุณาเลือกยศ"},firstName:{required:!0,trigger:"input",message:"กรุณากรอกชื่อ"},lastName:{required:!0,trigger:"input",message:"กรุณากรอกนามสกุล"},position:{type:"number",required:!0,trigger:["input","change"],message:"กรุณาเลือกตำแหน่ง"},region:{type:"number",required:!0,trigger:["input","change"],message:"กรุณาเลือกภาค"},province:{type:"number",required:!0,trigger:["input","change"],message:"กรุณาเลือกจังหวัด"},department:{type:"number",required:!0,trigger:["input","change"],message:"กรุณาเลือกสังกัด"}}),n=i({phone:null,pwd:null,rePwd:null,rank:null,firstName:null,lastName:null,position:null,region:null,province:null,department:null}),P=i([]),$=i([]),q=i([]);i([]),i([]);const b=i([]),p=i([]),h=i(!1);Z(()=>{O(),C(),S()});const O=()=>{_.get("GetRanks").then(o=>{P.value=o.data})},C=()=>{_.get("GetPositionType").then(o=>{$.value=o.data})},S=()=>{_.get("GetRegion").then(o=>{q.value=o.data})},j=()=>{if(n.value.province=null,n.value.department=null,b.value=[],p.value=[],n.value.region){const o={region_id:n.value.region};_.get("getSelectRegion",{params:o}).then(t=>{b.value=t.data})}else b.value=[],p.value=[],n.value.province=null,n.value.department=null},z=()=>{if(n.value.department=null,p.value=[],n.value.province){const o={region_id:n.value.region,province_id:n.value.province};_.get("GetUnits",{params:o}).then(t=>{p.value=t.data})}else p.value=[],n.value.department=null};function H(){U.value?.validate(o=>{if(o)w.error("กรุณากรอกข้อมูลให้ครบถ้วน");else{v.value=!0;const t={tel:n.value.phone,password:n.value.pwd,rank_id:n.value.rank,position_type:n.value.position,first_name:n.value.firstName,last_name:n.value.lastName,region:n.value.region,province:n.value.province,unit:n.value.department};_.post("auth/register",t).then(d=>{d.data.success&&(w.success("สมัครสมาชิกสำเร็จ"),V.login(n.value.phone,n.value.pwd),v.value=!1)}).catch(d=>{w.error(d.response.data.mes),v.value=!1})}})}return(o,t)=>{const d=_e,g=ge,u=me,s=pe,x=K,E=F,y=se,N=te,f=ae,R=ie,I=fe,L=de,M=ue,D=oe,J=ce;return ne(),ee("div",null,[e(d,{depth:"3",class:"text-center mb-6"},{default:l(()=>[c(m(o.$t("login.registerTitle")),1)]),_:1}),e(J,{ref_key:"formRef",ref:U,rules:a(G),model:a(n),"show-label":!1},{default:l(()=>[le("div",ve,[e(N,{title:"ข้อมูลการเข้าสู่ระบบ",class:"mb-3"},{default:l(()=>[e(y,{cols:"1 700:3 ","x-gap":12},{default:l(()=>[e(s,null,{default:l(()=>[e(u,{path:"phone"},{default:l(()=>[e(g,{value:a(n).phone,"onUpdate:value":t[0]||(t[0]=r=>a(n).phone=r),clearable:"",placeholder:"กรอกเบอร์โทรศัพท์"},null,8,["value"])]),_:1})]),_:1}),e(s,null,{default:l(()=>[e(u,{path:"pwd"},{default:l(()=>[e(g,{value:a(n).pwd,"onUpdate:value":t[1]||(t[1]=r=>a(n).pwd=r),type:"password",placeholder:o.$t("login.passwordPlaceholder"),clearable:"","show-password-on":"click"},{"password-invisible-icon":l(()=>[e(x)]),"password-visible-icon":l(()=>[e(E)]),_:1},8,["value","placeholder"])]),_:1})]),_:1}),e(s,null,{default:l(()=>[e(u,{path:"rePwd"},{default:l(()=>[e(g,{value:a(n).rePwd,"onUpdate:value":t[2]||(t[2]=r=>a(n).rePwd=r),type:"password",placeholder:o.$t("login.checkPasswordPlaceholder"),clearable:"","show-password-on":"click"},{"password-invisible-icon":l(()=>[e(x)]),"password-visible-icon":l(()=>[e(E)]),_:1},8,["value","placeholder"])]),_:1})]),_:1})]),_:1})]),_:1}),e(N,{title:"ข้อมูลส่วนตัว",class:"mb-3"},{default:l(()=>[e(y,{cols:"1 700:2","x-gap":12},{default:l(()=>[e(s,null,{default:l(()=>[e(u,{path:"rank"},{default:l(()=>[e(f,{value:a(n).rank,"onUpdate:value":t[3]||(t[3]=r=>a(n).rank=r),options:a(P),placeholder:"เลือกยศ","label-field":"rank_name","value-field":"rank_id",clearable:"",filterable:""},null,8,["value","options"])]),_:1})]),_:1}),e(s,null,{default:l(()=>[e(u,{path:"position"},{default:l(()=>[e(f,{value:a(n).position,"onUpdate:value":t[4]||(t[4]=r=>a(n).position=r),options:a($),placeholder:"เลือกตำแหน่ง","label-field":"position_type","value-field":"position_id",clearable:"",filterable:""},null,8,["value","options"])]),_:1})]),_:1}),e(s,null,{default:l(()=>[e(u,{path:"firstName"},{default:l(()=>[e(g,{value:a(n).firstName,"onUpdate:value":t[5]||(t[5]=r=>a(n).firstName=r),clearable:"",placeholder:"กรอกชื่อ"},null,8,["value"])]),_:1})]),_:1}),e(s,null,{default:l(()=>[e(u,{path:"lastName"},{default:l(()=>[e(g,{value:a(n).lastName,"onUpdate:value":t[6]||(t[6]=r=>a(n).lastName=r),clearable:"",placeholder:"กรอกนามสกุล"},null,8,["value"])]),_:1})]),_:1})]),_:1})]),_:1}),e(N,{title:"ข้อมูลสังกัด",class:"mb-3"},{default:l(()=>[e(y,{cols:"1 700:3","x-gap":12},{default:l(()=>[e(s,null,{default:l(()=>[e(u,{path:"region"},{default:l(()=>[e(f,{value:a(n).region,"onUpdate:value":[t[7]||(t[7]=r=>a(n).region=r),j],options:a(q),placeholder:"เลือกภาค","label-field":"region_name","value-field":"region_id",clearable:"",filterable:""},null,8,["value","options"])]),_:1})]),_:1}),e(s,null,{default:l(()=>[e(u,{path:"province"},{default:l(()=>[e(f,{value:a(n).province,"onUpdate:value":[t[8]||(t[8]=r=>a(n).province=r),z],options:a(b),placeholder:"เลือกจังหวัด",clearable:"",filterable:"","label-field":"province_name","value-field":"province_id",disabled:!a(n).region},null,8,["value","options","disabled"])]),_:1})]),_:1}),e(s,null,{default:l(()=>[e(u,{path:"department"},{default:l(()=>[e(f,{value:a(n).department,"onUpdate:value":t[9]||(t[9]=r=>a(n).department=r),options:a(p),placeholder:"เลือกสังกัด",clearable:"",filterable:"","label-field":"unit_name","value-field":"unit_id",disabled:!a(n).province||a(p).length===0},null,8,["value","options","disabled"])]),_:1})]),_:1})]),_:1})]),_:1})]),e(u,null,{default:l(()=>[e(D,{vertical:"",size:20,class:"w-full"},{default:l(()=>[e(I,{checked:a(h),"onUpdate:checked":t[10]||(t[10]=r=>re(h)?h.value=r:null)},{default:l(()=>[c(m(o.$t("login.readAndAgree"))+" ",1),e(R,{type:"primary",text:""},{default:l(()=>[c(m(o.$t("login.userAgreement")),1)]),_:1})]),_:1},8,["checked"]),e(R,{block:"",type:"primary",onClick:H,disabled:!a(h),loading:a(v)},{default:l(()=>[c(m(o.$t("login.signUp")),1)]),_:1},8,["disabled","loading"]),e(M,{justify:"center"},{default:l(()=>[e(L,null,{default:l(()=>[c(m(o.$t("login.haveAccountText")),1)]),_:1}),e(R,{text:"",type:"primary",onClick:B},{default:l(()=>[c(m(o.$t("login.signIn")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["rules","model"])])}}});export{$e as _};

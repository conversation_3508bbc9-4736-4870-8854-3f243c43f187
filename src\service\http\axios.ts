import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import axios from 'axios'

// สร้าง axios instance
const axiosInstance: AxiosInstance = axios.create({
  baseURL: window.location.origin === import.meta.env.LOCAL_HOST
    ? import.meta.env.LOCAL_BASE_URL || 'http://127.0.0.1:3101/api'
    : window.location.origin === import.meta.env.PRO_HOST
      ? import.meta.env.PRO_BASE_URL
      : import.meta.env.UAT_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// เพิ่ม interceptors (ถ้าต้องการ)
axiosInstance.interceptors.request.use(
  (config) => {
    // คุณสามารถใส่ token หรือแก้ไข config ได้ที่นี่
    return config
  },
  error => Promise.reject(error),
)

axiosInstance.interceptors.response.use(
  response => response,
  (error) => {
    // จัดการ error จาก response
    return Promise.reject(error)
  },
)

// สร้าง object http สำหรับการใช้งาน
const http = {
  get<T = any, R = AxiosResponse<T>>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return axiosInstance.get(url, config)
  },
  post<T = any, R = AxiosResponse<T>>(
    url: string,
    data?: T,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return axiosInstance.post(url, data, config)
  },
  put<T = any, R = AxiosResponse<T>>(
    url: string,
    data?: T,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return axiosInstance.put(url, data, config)
  },
  delete<R = AxiosResponse>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<R> {
    return axiosInstance.delete(url, config)
  },
}

export default http

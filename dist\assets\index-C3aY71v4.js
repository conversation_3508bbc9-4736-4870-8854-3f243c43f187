import{_ as Y,a as Z}from"./Pagination.vue_vue_type_script_setup_true_lang-uFMUKmhT.js";import{x as ee,s as F,b as M,e as A,d as te,y as ne,z as ae,A as le,C as se,r,D as T,f as e,E as k,j as i,B as b,fi as oe,G as U,H as D,o as ie,w as l,J as ce,K as re,t as f,i as o,L as ue,M as pe,N as _e,fj as de}from"./index-DSmp6iCg.js";import{_ as me}from"./peoples-Doj-bBmD.js";import{_ as ge}from"./text-Df11blX7.js";import{_ as fe,a as ve}from"./FormItem-Be7I1W2B.js";import{_ as ye,a as he}from"./Grid-lBi2xqol.js";import{_ as xe}from"./Input-D61U907N.js";import"./Checkbox-CuJqEfdi.js";import"./Forward-XU0gTivF.js";const ke={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function be(u,n){return M(),F("svg",ke,n[0]||(n[0]=[A("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4",d:"M43 11L16.875 37L5 25.182"},null,-1)]))}const De=ee({name:"icon-park-outline-check",render:be}),Se={class:"text-lg font-medium"};function S(u){return typeof u=="function"||Object.prototype.toString.call(u)==="[object Object]"&&!de(u)}const Me=te({__name:"index",setup(u){const{t:n}=ne(),p=ae(),j=le(),G=se("(max-width: 768px)"),d=r(!1),C=r(1),v=r(10),y=r(1),$=r([]),c=r({title:"",type:""}),L=T(()=>[{label:n("all"),value:""},{label:n("group"),value:"group"},{label:n("supergroup"),value:"supergroup"},{label:n("channel"),value:"channel"},{label:n("private"),value:"private"}]),N=T(()=>[{title:n("no."),align:"center",key:"index",render:(t,a)=>v.value*(y.value-1)+a+1},{title:n("status"),key:"isActive",align:"center",render:t=>{let a,s;return t.isActive?e(k,{type:"success"},S(a=n("active"))?a:{default:()=>[a]}):e(k,{type:"error"},S(s=n("inactive"))?s:{default:()=>[s]})}},{title:n("type"),key:"type",align:"center",render:t=>{let a;return e(k,{class:"uppercase",type:{group:"default",supergroup:"info",channel:"warning",private:"error"}[t.type]||"default"},S(a=n(t.type))?a:{default:()=>[a]})}},{title:n("groupname"),key:"title",align:"center",render:t=>e("div",null,[t.title||"-"])},{title:n("usdtAllDeposit"),key:"usdtAllDeposit",align:"center",render:t=>e("div",{class:"flex items-center justify-center gap-2"},[e("span",{class:"font-semibold text-sm "},[parseFloat(t.usdtDeposit?.toString()||"0").toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2})]),e("span",{class:"text-gray-500 text-xs"},[i("USDT")])])},{title:n("usdtAllWithdraw"),key:"usdtAllWithdraw",align:"center",render:t=>e("div",{class:"flex items-center justify-center gap-2"},[e("span",{class:"font-semibold text-sm "},[parseFloat(t.usdtWithdraw?.toString()||"0").toFixed(2)]),e("span",{class:"text-gray-500 text-xs"},[i("USDT")])])},{title:n("usdtBalance"),key:"usdtBalance",align:"center",render:t=>e("div",{class:"flex items-center justify-center gap-2"},[e("span",{class:"font-semibold text-sm "},[parseFloat(t.usdtBalance?.toString()||"0").toFixed(2)]),e("span",{class:"text-gray-500 text-xs"},[i("USDT")])])},{title:n("addedby"),key:"addedByUsername",align:"center",render:t=>e("div",{class:"text-sm"},[t.addedByUsername||"-"])},{title:n("lastseendate"),key:"lastSeenAt",align:"center",render:t=>t.lastSeenAt?e("div",{class:"text-sm"},[new Date(t.lastSeenAt).toLocaleString()]):e("div",null,[i("-")])},{title:n("manage"),align:"center",key:"actions",render:t=>e(U,{justify:"center"},{default:()=>[!t.isActive&&e(b,{type:"success",size:"small",onClick:()=>P(t)},{default:()=>[e("div",{class:"flex items-center gap-1"},[e(De,null,null),i(" "),n("approve")])]}),t.isActive&&e(b,{type:"error",size:"small",onClick:()=>z(t)},{default:()=>[e("div",{class:"flex items-center gap-1"},[e(oe,null,null),i(" "),n("revoke")])]})]})}]),P=t=>{j.info({title:n("confirmactivate"),content:n("areyousureactivate"),positiveText:n("activate"),negativeText:n("cancel"),onPositiveClick:()=>{const a={ID:t._id,isActive:!0};D.post("v1/group-setting/update",a).then(s=>{s.data.success&&(_(),p.success(n("saveDataSuccess")))}).catch(()=>{p.error(n("deactivatefailed"))})}})},z=t=>{j.warning({title:n("confirmdeactivate"),content:n("areyousuredeactivate"),positiveText:n("deactivate"),negativeText:n("cancel"),onPositiveClick:()=>{const a={ID:t._id,isActive:!1};D.post("v1/group-setting/update",a).then(s=>{s.data.success&&(_(),p.success(n("saveDataSuccess")))}).catch(()=>{p.error(n("deactivatefailed"))})}})},_=async()=>{try{d.value=!0;const t={page:y.value,limit:v.value,title:c.value.title,type:c.value.type},{data:a}=await D.get("v1/group-setting/getGroupList",{params:t});$.value=a.data||[],C.value=a.total||0,d.value=!1}catch{p.error(n("fetchfailed")),d.value=!1}},V=()=>{c.value={title:"",type:""}},I=(t,a)=>{v.value=a,y.value=t,_()};return ie(()=>{_()}),(t,a)=>{const s=me,O=re,R=ge,m=U,h=ce,W=xe,w=ve,g=he,E=ue,H=pe,B=b,J=_e,K=ye,Q=fe,q=Y,X=Z;return M(),F("div",null,[e(h,null,{default:l(()=>[e(m,{vertical:"",size:"large"},{default:l(()=>[e(h,null,{default:l(()=>[e(m,{justify:"space-between",align:"center"},{default:l(()=>[e(m,{align:"center"},{default:l(()=>[e(O,{color:"#1a8a93"},{default:l(()=>[e(s)]),_:1}),A("div",null,[A("p",Se,f(t.$t("grouplist")),1),e(R,{depth:"3"},{default:l(()=>[i(f(t.$t("manageGroupsAndChannels")),1)]),_:1})])]),_:1})]),_:1})]),_:1}),e(h,null,{default:l(()=>[e(Q,{ref:"formRef",model:o(c),"label-placement":o(G)?"top":"left","show-feedback":!1},{default:l(()=>[e(K,{cols:"1 600:2 1000:4","x-gap":16,"y-gap":16},{default:l(()=>[e(g,null,{default:l(()=>[e(w,{label:t.$t("groupname"),path:"title"},{default:l(()=>[e(W,{value:o(c).title,"onUpdate:value":a[0]||(a[0]=x=>o(c).title=x),placeholder:t.$t("enterGroupName"),clearable:""},{prefix:l(()=>[e(s)]),_:1},8,["value","placeholder"])]),_:1},8,["label"])]),_:1}),e(g,null,{default:l(()=>[e(w,{label:t.$t("type"),path:"type"},{default:l(()=>[e(E,{value:o(c).type,"onUpdate:value":a[1]||(a[1]=x=>o(c).type=x),placeholder:t.$t("selecttype"),clearable:"",options:o(L)},null,8,["value","placeholder","options"])]),_:1},8,["label"])]),_:1}),e(g,null,{default:l(()=>[e(B,{type:"default",onClick:_,block:""},{icon:l(()=>[e(H)]),default:l(()=>[i(" "+f(t.$t("search")),1)]),_:1})]),_:1}),e(g,null,{default:l(()=>[e(B,{type:"default",onClick:V,block:"",secondary:""},{icon:l(()=>[e(J)]),default:l(()=>[i(" "+f(t.$t("refresh")),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["model","label-placement"])]),_:1}),e(q,{"scroll-x":1200,columns:o(N),data:o($),loading:o(d)},null,8,["columns","data","loading"]),e(m,{justify:"end"},{default:l(()=>[e(X,{count:o(C),onChange:I},null,8,["count"])]),_:1})]),_:1})]),_:1})])}}});export{Me as default};

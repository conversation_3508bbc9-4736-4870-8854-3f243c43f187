import{x as o,s as t,b as r,e}from"./index-DSmp6iCg.js";const s={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function l(i,n){return r(),t("svg",s,n[0]||(n[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M6 6v36h36"}),e("path",{d:"m14 34l8-16l10 9L42 6"})],-1)]))}const p=o({name:"icon-park-outline-chart-line",render:l}),a={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function d(i,n){return r(),t("svg",a,n[0]||(n[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M43 5L29.7 43l-7.6-17.1L5 18.3z"}),e("path",{"stroke-linecap":"round",d:"M43 5L22.1 25.9"})],-1)]))}const u=o({name:"icon-park-outline-send",render:d});export{u as _,p as a};

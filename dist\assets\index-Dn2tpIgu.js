import{_ as pe,a as ie}from"./Pagination.vue_vue_type_script_setup_true_lang-uFMUKmhT.js";import{d as _e,y as me,z as ce,C as ge,r as s,ff as C,fg as E,I as fe,D as ve,f as e,j as p,B as j,G as z,o as ye,s as he,b as Te,w as a,e as v,J as be,K as Be,t as c,i as l,L as Ne,fh as De,M as Ue,N as ke,fD as He,l as Re,R as Se,H}from"./index-DSmp6iCg.js";import{_ as Me}from"./dollar-B-mq68Qd.js";import{_ as Pe}from"./edit-DdCXKTed.js";import{m as R}from"./index-BqRpacQ4.js";import{t as F,d as G,_ as xe}from"./InputNumber-B2VNskBn.js";import{_ as $e}from"./text-Df11blX7.js";import{_ as we,a as Ye}from"./FormItem-Be7I1W2B.js";import{_ as Ce,a as Ee}from"./Grid-lBi2xqol.js";import{_ as je}from"./DatePicker-D6AT7i-o.js";import{_ as ze}from"./Input-D61U907N.js";import"./Checkbox-CuJqEfdi.js";import"./Forward-XU0gTivF.js";import"./moment-zH0z38ay.js";const Fe={class:"text-lg font-medium"},Ge={class:"flex justify-end"},nt=_e({__name:"index",setup(qe){const{t:o,locale:B}=me(),g=ce(),q=ge("(max-width: 768px)"),S=s(B.value==="thTH"?F:C),M=s(B.value==="thTH"?G:E);fe(B,t=>{S.value=t==="thTH"?F:C,M.value=t==="thTH"?G:E});const f=s(!1),N=s(!1),_=s(!1),P=s(1),D=s(10),U=s(1),r=s({groupName:null,dateRange:null}),A=()=>{r.value={groupName:"",dateRange:null}},x=s([]),$=s(),d=s({_id:"",groupName:null,rateTHBPerUSDT:null,updatedByName:"",updatedByUsername:""}),I={groupName:{required:!0,message:o("groupNameRequired"),trigger:["blur","input"]},rateTHBPerUSDT:{required:!0,type:"number",message:o("rateRequired"),trigger:["blur","change"]}},V=ve(()=>[{title:o("no."),key:"index",align:"center",render:(t,n)=>D.value*(U.value-1)+n+1},{title:o("groupName"),key:"groupName",align:"center",render:t=>e("div",null,[t.group_name||"-"])},{title:o("rate"),key:"rateTHBPerUSDT",align:"center",render:t=>e("div",{class:"flex items-center justify-center gap-2"},[e("img",{src:"/images/country/th.webp",alt:"THB",class:"w-5 rounded"},null),e("span",{class:"font-semibold text-sm"},[parseFloat(t.rateTHBPerUSDT?.toString()||"0").toFixed(2),p(" ฿")]),e("span",{class:"text-gray-500 text-xs"},[p("/ USDT")])])},{title:o("updatedBy"),key:"updatedBy",align:"center",render:t=>e("div",{class:"text-center"},[e("div",{class:"font-medium text-sm"},[t.updatedByName]),e("div",{class:"text-gray-500 text-xs"},[p("@"),t.updatedByUsername])])},{title:o("updatedAt"),key:"updatedAt",align:"center",render:t=>e("div",{class:"text-sm"},[L(t.updatedAt)])},{title:o("actions"),key:"actions",align:"center",render:t=>e(z,{justify:"center"},{default:()=>[e(j,{size:"small",type:"primary",onClick:()=>J(t)},{default:()=>[e("div",{class:"flex items-center gap-1"},[e(Pe,null,null),p(" "),o("edit")])]})]})}]),L=t=>t?R(t).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm:ss"):"-",J=t=>{d.value={_id:t._id,groupName:t.group_name,rateTHBPerUSDT:Number(t.rateTHBPerUSDT),updatedByName:t.updatedByName,updatedByUsername:t.updatedByUsername},_.value=!0},K=()=>{$.value?.validate(t=>{if(!t){N.value=!0;const n={ID:d.value._id,groupName:d.value.groupName,rateTHBPerUSDT:d.value.rateTHBPerUSDT};H.post("v1/rate-setting/update",n).then(i=>{i.data.success?(g.success(i.data.message||o("updatesuccess")),_.value=!1,y()):g.error(i.data.message||o("updatefailed"))}).catch(i=>{g.error(i.response?.data?.message||o("updatefailed"))}).finally(()=>{N.value=!1})}})},y=async()=>{f.value=!0;try{const t={page:U.value,perPage:D.value,groupName:r.value.groupName||void 0,startDate:r.value.dateRange?.[0]?R(r.value.dateRange[0]).format("YYYY-MM-DD"):void 0,endDate:r.value.dateRange?.[1]?R(r.value.dateRange[1]).format("YYYY-MM-DD"):void 0},{data:n}=await H.get("v1/rate-setting/list",{params:t});x.value=n.data,P.value=n.total,f.value=!1}catch{g.error(o("fetchfailed")),f.value=!1}},O=(t,n)=>{U.value=t,D.value=n,y()},w=s([]),Q=async()=>{try{const t=await H.get("v1/group-setting/getGroup");if(t.data.status){w.value=t.data.data||[];return}}catch{g.error(o("fetchfailed"))}finally{f.value=!1}};return ye(()=>{y(),Q()}),(t,n)=>{const i=Me,W=Be,X=$e,h=z,k=be,Z=Ne,m=Ye,T=Ee,ee=je,te=De,ae=Ue,b=j,ne=ke,le=Ce,Y=we,oe=pe,se=ie,re=ze,ue=xe,de=Se;return Te(),he("div",null,[e(k,null,{default:a(()=>[e(h,{vertical:"",size:"large"},{default:a(()=>[e(k,null,{default:a(()=>[e(h,{justify:"space-between",align:"center"},{default:a(()=>[e(h,{align:"center"},{default:a(()=>[e(W,{color:"#1a8a93"},{default:a(()=>[e(i)]),_:1}),v("div",null,[v("p",Fe,c(t.$t("rateSettingManagement")),1),e(X,{depth:"3"},{default:a(()=>[p(c(t.$t("manageExchangeRates")),1)]),_:1})])]),_:1})]),_:1})]),_:1}),e(k,null,{default:a(()=>[e(Y,{ref:"formRef",model:l(r),"label-placement":l(q)?"top":"left","show-feedback":!1},{default:a(()=>[e(le,{cols:"1 600:2 1000:4","x-gap":16,"y-gap":16},{default:a(()=>[e(T,null,{default:a(()=>[e(m,{label:t.$t("group"),path:"groupName"},{default:a(()=>[e(Z,{value:l(r).groupName,"onUpdate:value":n[0]||(n[0]=u=>l(r).groupName=u),placeholder:t.$t("selectGroupName"),clearable:"",options:l(w),"label-field":"title","value-field":"title",filterable:""},null,8,["value","placeholder","options"])]),_:1},8,["label"])]),_:1}),e(T,null,{default:a(()=>[e(m,{label:t.$t("dateRange"),path:"dateRange"},{default:a(()=>[e(te,{locale:l(S),"date-locale":l(M)},{default:a(()=>[e(ee,{value:l(r).dateRange,"onUpdate:value":n[1]||(n[1]=u=>l(r).dateRange=u),type:"daterange",clearable:"",format:"dd/MM/yyyy",style:{width:"100%"}},null,8,["value"])]),_:1},8,["locale","date-locale"])]),_:1},8,["label"])]),_:1}),e(T,null,{default:a(()=>[e(m,{label:" ",path:"search"},{default:a(()=>[e(b,{type:"default",onClick:y,block:""},{icon:a(()=>[e(ae)]),default:a(()=>[p(" "+c(t.$t("search")),1)]),_:1})]),_:1})]),_:1}),e(T,null,{default:a(()=>[e(m,{label:" ",path:"refresh"},{default:a(()=>[e(b,{type:"default",onClick:A,block:"",secondary:""},{icon:a(()=>[e(ne)]),default:a(()=>[p(" "+c(t.$t("refresh")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model","label-placement"])]),_:1}),e(oe,{"scroll-x":1200,columns:l(V),data:l(x),loading:l(f)},null,8,["columns","data","loading"]),v("div",Ge,[e(se,{count:l(P),onChange:O},null,8,["count"])])]),_:1})]),_:1}),e(de,{show:l(_),"onUpdate:show":n[5]||(n[5]=u=>Re(_)?_.value=u:null),preset:"dialog",title:t.$t("editRate")},{action:a(()=>[e(h,null,{default:a(()=>[e(b,{onClick:n[4]||(n[4]=u=>_.value=!1)},{default:a(()=>[p(c(t.$t("cancel")),1)]),_:1}),e(b,{type:"primary",onClick:K,loading:l(N)},{default:a(()=>[p(c(t.$t("save")),1)]),_:1},8,["loading"])]),_:1})]),default:a(()=>[e(Y,{ref_key:"editFormRef",ref:$,model:l(d),rules:I,"label-placement":"top"},{default:a(()=>[e(m,{label:t.$t("groupName"),path:"groupName"},{default:a(()=>[e(re,{value:l(d).groupName,"onUpdate:value":n[2]||(n[2]=u=>l(d).groupName=u),size:"large",disabled:"",placeholder:t.$t("enterGroupName")},null,8,["value","placeholder"])]),_:1},8,["label"]),e(m,{label:t.$t("rateTHBPerUSDT"),path:"rateTHBPerUSDT"},{default:a(()=>[e(ue,{value:l(d).rateTHBPerUSDT,"onUpdate:value":n[3]||(n[3]=u=>l(d).rateTHBPerUSDT=u),min:1,max:100,step:.01,precision:2,size:"large",style:{width:"100%"},placeholder:t.$t("enterRate")},{prefix:a(()=>n[6]||(n[6]=[v("img",{src:He,alt:"THB",class:"w-4 rounded mt-1 mr-2"},null,-1)])),suffix:a(()=>n[7]||(n[7]=[v("span",null,"฿ / USDT ",-1)])),_:1},8,["value","placeholder"])]),_:1},8,["label"])]),_:1},8,["model"])]),_:1},8,["show","title"])])}}});export{nt as default};

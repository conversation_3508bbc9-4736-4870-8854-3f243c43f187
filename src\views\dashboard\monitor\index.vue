<template>
  <div>
    <n-grid cols="1 700:2 1024:3 " :x-gap="12" :y-gap="12">
       <n-gi>
        <n-card>
          <n-space justify="space-between" align="center">
            <n-statistic :label="$t('depositTodayTHB')">
              <n-number-animation :from="0" :to="monitorData.depositTodayTH" show-separator />
              <template #suffix>
                <span class="text-sm text-gray-500">฿</span>
              </template>
            </n-statistic>
            <n-icon color="#d03050" size="42">
              <icon-park-outline-exchange />
            </n-icon>
          </n-space>
        </n-card>
      </n-gi>

       <n-gi>
        <n-card>
          <n-space justify="space-between" align="center">
            <n-statistic :label="$t('exchangeTodayUSDT')">
              <n-number-animation :from="0" :to="monitorData.exchangeToday" show-separator />
              <template #suffix>
                <span class="text-sm text-gray-500">USDT</span>
              </template>
            </n-statistic>
            <n-icon :color="primaryColor" size="42">
              <icon-park-outline-wallet />
            </n-icon>
          </n-space>
        </n-card>
      </n-gi>

      <n-gi>
        <n-card>
          <n-space justify="space-between" align="center">
            <n-statistic :label="$t('withdrawTodayUSDT')">
              <n-number-animation :from="0" :to="monitorData.withdrawTodayUSD" show-separator />
              <template #suffix>
                <span class="text-sm text-gray-500">USDT</span>
              </template>
            </n-statistic>
            <n-icon color="#18a058" size="42">
              <icon-park-outline-send />
            </n-icon>
          </n-space>
        </n-card>
      </n-gi>

        <n-gi>
        <n-card>
          <n-space justify="space-between" align="center">
            <n-statistic :label="$t('depositMonthTHB')">
              <n-number-animation :from="0" :to="monitorData.depositMonthTH" show-separator />
              <template #suffix>
                <span class="text-sm text-gray-500">฿</span>
              </template>
            </n-statistic>
            <n-icon color="#06b6d4" size="42">
              <icon-park-outline-chart-line />
            </n-icon>
          </n-space>
        </n-card>
      </n-gi>

      <n-gi>
        <n-card>
          <n-space justify="space-between" align="center">
            <n-statistic :label="$t('exchangeMonthUSDT')">
              <n-number-animation :from="0" :to="monitorData.exchangeMonth" show-separator />
              <template #suffix>
                <span class="text-sm text-gray-500">USDT</span>
              </template>
            </n-statistic>
            <n-icon color="#f59e0b" size="42">
              <icon-park-outline-income />
            </n-icon>
          </n-space>
        </n-card>
      </n-gi>

      <n-gi>
        <n-card>
          <n-space justify="space-between" align="center">
            <n-statistic :label="$t('withdrawMonthUSDT')">
              <n-number-animation :from="0" :to="monitorData.withdrawMonthUSD" show-separator />
              <template #suffix>
                <span class="text-sm text-gray-500">USDT</span>
              </template>
            </n-statistic>
            <n-icon color="#8b5cf6" size="42">
              <icon-park-outline-expenses />
            </n-icon>
          </n-space>
        </n-card>
      </n-gi>

   
      <n-gi span="8">
        <n-card>
          <div
            class="flex flex-wrap sm:flex-nowrap justify-center items-center gap-3"
          >
            <p class="text-center text-lg font-bold">{{ $t('dashboard.annualDepositWithdrawal') }}</p>
            <n-date-picker
              type="year"
              :placeholder="$t('dashboard.selectYear')"
              v-model:value="selectYears"
              class="w-28 z-10"
            />
          </div>
          <Chart :year="selectYears"/>
        </n-card>
      </n-gi>

      <n-gi span="8">
        <n-card>
          <div
            class="flex flex-wrap sm:flex-nowrap justify-center items-center gap-3"
          >
            <p class="text-center text-lg font-bold">{{ $t('dashboard.thbUsdtExchangeVolume') }}</p>
            <n-date-picker
              type="year"
              :placeholder="$t('dashboard.selectYear')"
              v-model:value="selectYear"
              class="w-28 z-10"
            />
          </div>

          <Chart2 :year="selectYear"/>
        </n-card>
      </n-gi>
    </n-grid>
  </div>
</template>
<script setup lang="ts">
import http from "@/service/axios";
import Chart from "../monitor/components/chart.vue";
import Chart2 from "../monitor/components/chart2.vue";
import { useAppStore } from "@/store";

const appStore = useAppStore();
const primaryColor = computed(() => appStore.primaryColor);

const monitorData = ref({
});

const getMonitorData = async () => {
  try {
    const { data: res } = await http.get("v1/dashboard/getMonitor");
    monitorData.value = res;
  } catch (error) {
    console.error('Failed to fetch monitor data:', error);
  }
};

watch(
  () => appStore.primaryColor,
  (newColor) => {
    primaryColor.value = newColor;
  }
);

const selectYear = ref(new Date().getTime());
const selectYears = ref(new Date().getTime());

onMounted(() => {
  getMonitorData();
});
</script>
<script setup lang="ts">
import type { FormInst } from "naive-ui";
import { useAuthStore } from "@/store";
import { local } from "@/utils";

const emit = defineEmits(["update:modelValue"]);

const authStore = useAuthStore();
const message = useMessage();
function toOtherForm(type: any) {
  emit("update:modelValue", type);
}
const { t } = useI18n();
const rules = ref({
  // account: {
  //   required: true,
  //   trigger: "input",
  //   validator: (rule: any, value: string) => {
  //     if (!value) return new Error(t("login.telRuleTip"));

  //     if (!/^\d+$/.test(value)) {
  //       return new Error("เบอร์โทรต้องเป็นตัวเลขเท่านั้น");
  //     }

  //     const phoneRegex = /^[0-9]{10}$/;
  //     if (!phoneRegex.test(value)) {
  //       return new Error("เบอร์โทรต้องยาว 10 ตัวเท่านั้น");
  //     }

  //     return true;
  //   },
  // },
  pwd: {
    required: true,
    trigger: "input",
    validator: (rule: any, value: string) => {
      if (!value) return new Error(t("login.passwordRuleTip"));
      if (value.length < 6) {
        return new Error("รหัสผ่านต้องยาวอย่างน้อย 6 ตัวอักษร");
      }
      return true;
    },
  },
});
const formValue = ref({
  account: null,
  pwd: null,
});
const isRemember = ref(false);
const isLoading = ref(false);

const formRef = ref<FormInst | null>(null);

function handleLogin() {
  formRef.value?.validate(async (errors) => {
    if (errors) return;

    isLoading.value = true;
    const { account, pwd } = formValue.value;

    if (isRemember.value) local.set("loginAccount", { account, pwd });
    else local.remove("loginAccount");

    const result = await authStore.login(account, pwd);
    isLoading.value = false;

    if (!result.success && result.message) {
      message.error(result.message);
    }
  });
}
onMounted(() => {
  checkUserAccount();
});
function checkUserAccount() {
  const loginAccount = local.get("loginAccount");
  if (!loginAccount) return;

  formValue.value = loginAccount;
  isRemember.value = true;
}
</script>

<template>
  <div>
    <n-h2 depth="3" class="text-center">
      {{ $t("login.signInTitle") }}
    </n-h2>
    <n-form
      ref="formRef"
      :rules="rules"
      :model="formValue"
      :show-label="false"
      size="large"
      @keydown.enter.prevent="handleLogin"
    >
      <n-form-item path="account">
        <n-input
          v-model:value="formValue.account"
          clearable
          :placeholder="$t('login.accountPlaceholder')"
        >
          <template #prefix>
            <icon-park-outline-user class="mr-2"/>
          </template>
        </n-input>
      </n-form-item>
      <n-form-item path="pwd">
        <n-input
          v-model:value="formValue.pwd"
          type="password"
          :placeholder="$t('login.passwordPlaceholder')"
          clearable
          show-password-on="click"
        >
          <template #prefix>
            <icon-park-outline-lock class="mr-2"/>
          </template>
          <template #password-invisible-icon>
            <icon-park-outline-preview-close-one />
          </template>
          <template #password-visible-icon>
            <icon-park-outline-preview-open />
          </template>
        </n-input>
      </n-form-item>
      <n-space vertical :size="20">
        <div class="flex-y-center justify-between">
          <n-checkbox v-model:checked="isRemember">
            {{ $t("login.rememberMe") }}
          </n-checkbox>
          <!-- <n-button type="primary" text @click="toOtherForm('resetPwd')">
            {{ $t('login.forgotPassword') }}
          </n-button> -->
        </div>
        <n-button
          block
          type="primary"
          size="large"
          :loading="isLoading"
          :disabled="isLoading"
          @click="handleLogin"
          @keydown.enter="handleLogin"
        >
          {{ $t("login.signIn") }}
        </n-button>
        <!-- <n-flex justify="center">
          <n-text>{{ $t("login.noAccountText") }}</n-text>
          <n-button type="primary" text @click="toOtherForm('register')">
            {{ $t("login.signUp") }}
          </n-button>
        </n-flex> -->
      </n-space>
    </n-form>
  </div>
</template>

<style scoped></style>

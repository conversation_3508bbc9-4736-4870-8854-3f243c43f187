{"name": "kali-u9-admin", "type": "module", "version": "0.9.10", "private": true, "description": "a clean and concise back-end management template based on Vue3, Vite5, Typescript, and Naive UI.", "keywords": ["<PERSON><PERSON>", "Vue3", "admin", "admin-template", "vue-admin", "vue-admin-template", "Vite5", "Vite", "vite-admin", "TypeScript", "TS", "NaiveUI", "naive-ui", "naive-admin", "NaiveUI-Admin", "naive-ui-admin", "UnoCSS"], "scripts": {"dev": "vite --mode dev --port 9980", "dev:test": "vite --mode test", "dev:prod": "vite --mode prod", "build": "vite build --mode prod", "build:dev": "vite build --mode dev", "build:test": "vite build --mode test", "preview": "vite preview --port 9981", "lint": "eslint . && vue-tsc --noEmit", "lint:fix": "eslint . --fix", "lint:check": "npx @eslint/config-inspector", "sizecheck": "npx vite-bundle-visualizer"}, "dependencies": {"@vueuse/core": "^12.3.0", "alova": "^3.2.7", "axios": "^1.7.9", "colord": "^2.9.3", "echarts": "^5.6.0", "md-editor-v3": "^5.1.1", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^4.2.0", "quill": "^2.0.3", "radash": "^12.1.0", "vue": "^3.5.13", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^11.0.1", "vue-router": "^4.5.0"}, "devDependencies": {"@antfu/eslint-config": "^3.12.1", "@iconify-json/icon-park-outline": "^1.2.2", "@iconify/vue": "^4.3.0", "@types/axios": "^0.9.36", "@types/node": "^22.10.5", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "eslint": "^9.17.0", "lint-staged": "^15.3.0", "naive-ui": "^2.40.4", "sass": "^1.83.1", "simple-git-hooks": "^2.11.1", "typescript": "^5.7.2", "unocss": "^0.65.3", "unplugin-auto-import": "^0.19.0", "unplugin-icons": "^0.22.0", "unplugin-vue-components": "^0.28.0", "vite": "^6.0.7", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "7.6.8", "vue-tsc": "^2.2.0"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "volta": {"node": "20.12.2"}}
<script setup lang="tsx">
import http from "@/service/axios";
const { t } = useI18n();
const message = useMessage();
const dialog = useDialog();

const isMobile = useMediaQuery("(max-width: 768px)");
const loading = ref(false);
const total = ref(1);
const perPage = ref(10);
const Page = ref(1);
const list = ref([
]);

// Search Model
const searchModel = ref({
  title: "",
  type: "",
});

const TypeOption = computed(() => [
  { label: t("all"), value: "" },
  { label: t("group"), value: "group" },
  { label: t("supergroup"), value: "supergroup" },
  { label: t("channel"), value: "channel" },
  { label: t("private"), value: "private" },
]);

const columns = computed(() => [
  {
    title: t("no."),
    align: "center",
    key: "index",
    render: (row, index) => {
      return perPage.value * (Page.value - 1) + index + 1;
    },
  },
  // {
  //   title: t("chatid"),
  //   key: "chatId",
  //   align: "center",
  //   render: (row) => {
  //     return <div class="text-sm font-mono">{row.chatId || "-"}</div>;
  //   },
  // },
  {
    title: t("status"),
    key: "isActive",
    align: "center",
    render: (row) => {
      return row.isActive ? (
        <n-tag type="success">{t("active")}</n-tag>
      ) : (
        <n-tag type="error">{t("inactive")}</n-tag>
      );
    },
  },
  {
    title: t("type"),
    key: "type",
    align: "center",
    render: (row) => {
      const typeColors = {
        group: "default",
        supergroup: "info",
        channel: "warning",
        private: "error",
      };
      return (
        <n-tag class="uppercase" type={typeColors[row.type] || "default"}>
          {t(row.type)}
        </n-tag>
      );
    },
  },
  {
    title: t("groupname"),
    key: "title",
    align: "center",
    render: (row) => {
      return <div >{row.title || "-"}</div>;
    },
  },
  {
    title: t("usdtAllDeposit"),
    key: "usdtAllDeposit",
    align: "center",
    render: (row: any) => {
      return (
        <div class="flex items-center justify-center gap-2">
          <span class="font-semibold text-sm ">
            {parseFloat(row.usdtDeposit?.toString() || "0").toLocaleString(
              "en-US",
              { minimumFractionDigits: 2, maximumFractionDigits: 2 }
            )}
          </span>
          <span class="text-gray-500 text-xs">USDT</span>
        </div>
      );
    },
  },
  {
    title: t("usdtAllWithdraw"),
    key: "usdtAllWithdraw",
    align: "center",
    render: (row: any) => {
      return (
        <div class="flex items-center justify-center gap-2">
          <span class="font-semibold text-sm ">
            {parseFloat(row.usdtWithdraw?.toString() || "0").toFixed(2)}
          </span>
          <span class="text-gray-500 text-xs">USDT</span>
        </div>
      );
    },
  },
  {
    title: t("usdtBalance"),
    key: "usdtBalance",
    align: "center",
    render: (row: any) => {
      return (
        <div class="flex items-center justify-center gap-2">
          <span class="font-semibold text-sm ">
            {parseFloat(row.usdtBalance?.toString() || "0").toFixed(2)}
          </span>
          <span class="text-gray-500 text-xs">USDT</span>
        </div>
      );
    },
  },
  {
    title: t("addedby"),
    key: "addedByUsername",
    align: "center",
    render: (row) => {
      return <div class="text-sm">{row.addedByUsername || "-"}</div>;
    },
  },
  {
    title: t("lastseendate"),
    key: "lastSeenAt",
    align: "center",
    render: (row) => {
      return row.lastSeenAt ? (
        <div class="text-sm">{new Date(row.lastSeenAt).toLocaleString()}</div>
      ) : (
        <div>-</div>
      );
    },
  },
  {
    title: t("manage"),
    align: "center",
    key: "actions",
    render: (row) => {
      return (
        <n-space justify="center">
          {!row.isActive && (
            <n-button
              type="success"
              size="small"
              onClick={() => handleActivateGroup(row)}
            >
              <div class="flex items-center gap-1">
                <icon-park-outline-check /> {t("approve")}
              </div>
            </n-button>
          )}
          {row.isActive && (
            <n-button
              type="error"
              size="small"
              onClick={() => handleDeactivateGroup(row)}
            >
              <div class="flex items-center gap-1">
                <icon-park-outline-close /> {t("revoke")}
              </div>
            </n-button>
          )}
        </n-space>
      );
    },
  },
]);

const handleActivateGroup = (item) => {
  dialog.info({
    title: t("confirmactivate"),
    content: t("areyousureactivate"),
    positiveText: t("activate"),
    negativeText: t("cancel"),
    onPositiveClick: () => {
      const obj = {
        ID: item._id,
        isActive: true
      };
      http.post('v1/group-setting/update', obj).then(response => {
        if(response.data.success){
          getData();
          message.success(t('saveDataSuccess'));
        }
        
      }).catch(() => {
        message.error(t('deactivatefailed'));
      }); 
    },
  });
};

const activateGroup = async (item) => {
  try {
    const response = await http.post("v1/Group/Activate", { id: item._id });
    if (response.data.status) {
      message.success(response.data.mes);
      getData();
    } else {
      message.error(response.data.mes);
    }
  } catch (error) {
    message.error(t("activatefailed"));
  }
};

const handleDeactivateGroup = (item) => {
  dialog.warning({
    title: t("confirmdeactivate"),
    content: t("areyousuredeactivate"),
    positiveText: t("deactivate"),
    negativeText: t("cancel"),
    onPositiveClick: () => {
      const obj = {
        ID: item._id,
        isActive: false
      };
      http.post('v1/group-setting/update', obj).then(response => {
        if(response.data.success){
          getData();
          message.success(t('saveDataSuccess'));
        }
        
      }).catch(() => {
        message.error(t('deactivatefailed'));
      }); 
      // deactivateGroup(item);
    },
  });
};

const deactivateGroup = async (item) => {
  try {
    const response = await http.post("v1/Group/Deactivate", { id: item._id });
    if (response.data.status) {
      message.success(response.data.mes);
      getData();
    } else {
      message.error(response.data.mes);
    }
  } catch (error) {
    message.error(t("deactivatefailed"));
  }
};

const getData = async () => {
  try {
    loading.value = true;
    const params = {
      page: Page.value,
      limit: perPage.value,
      title: searchModel.value.title,
      type: searchModel.value.type,
    };

    const {data: res} = await http.get("v1/group-setting/getGroupList", { params });
      list.value = res.data || [];
      total.value = res.total || 0;
      loading.value = false;
  } catch (error) {
    message.error(t("fetchfailed"));
    loading.value = false;
  } 
};

const handleRefresh = () => {
  searchModel.value = {
    title: "",
    type: "",
  };
};

const changePage = (page, size) => {
  perPage.value = size;
  Page.value = page;
  getData();
};

onMounted(() => {
  getData();
});
</script>

<template>
  <div>
    <n-card>
      <n-space vertical size="large">
        <!-- Header Section -->
        <n-card>
          <n-space justify="space-between" align="center">
            <n-space align="center">
              <n-avatar color="#1a8a93">
                <icon-park-outline-peoples />
              </n-avatar>
              <div>
                <p class="text-lg font-medium">{{ $t("grouplist") }}</p>
                <n-text depth="3">{{ $t("manageGroupsAndChannels") }}</n-text>
              </div>
            </n-space>       
          </n-space>
        </n-card>

        <!-- Search Form -->
        <n-card>
          <n-form
            ref="formRef"
            :model="searchModel"
            :label-placement="isMobile ? 'top' : 'left'"
            :show-feedback="false"
          >
            <n-grid cols="1 600:2 1000:4" :x-gap="16" :y-gap="16">
              <n-gi>
                <n-form-item :label="$t('groupname')" path="title">
                  <n-input
                    v-model:value="searchModel.title"
                    :placeholder="$t('enterGroupName')"
                    clearable
                  >
                    <template #prefix>
                      <icon-park-outline-peoples />
                    </template>
                  </n-input>
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-form-item :label="$t('type')" path="type">
                  <n-select
                    v-model:value="searchModel.type"
                    :placeholder="$t('selecttype')"
                    clearable
                    :options="TypeOption"
                  />
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-button type="default" @click="getData" block>
                  <template #icon>
                    <icon-park-outline-search />
                  </template>
                  {{ $t("search") }}
                </n-button>
              </n-gi>

              <n-gi>
                <n-button type="default" @click="handleRefresh" block secondary>
                  <template #icon>
                    <icon-park-outline-refresh />
                  </template>
                  {{ $t("refresh") }}
                </n-button>
              </n-gi>
            </n-grid>
          </n-form>
        </n-card>

        <!-- Data Table -->
        <n-data-table
          :scroll-x="1200"
          :columns="columns"
          :data="list"
          :loading="loading"
        />

        <!-- Pagination -->
        <n-space justify="end">
          <Pagination :count="total" @change="changePage" />
        </n-space>
      </n-space>
    </n-card>
  </div>
</template>
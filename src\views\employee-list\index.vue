<script setup lang="tsx">
import http from "@/service/axios";
import moment from "moment-timezone";
import { useBoolean } from "@/hooks";

const { t } = useI18n();
const message = useMessage();
const dialog = useDialog();
const isMobile = useMediaQuery("(max-width: 768px)");

const {
  bool: loading,
  setTrue: startLoading,
  setFalse: endLoading,
} = useBoolean(false);

const model = ref({
  username: "",
});
const showModal = ref(false);
const showAddModal = ref(false);
const itemsedit = ref({
  id: null,
  username: "",
  password: "",
  confirmPassword: ""
});
const addForm = ref({
  username: "",
  password: "",
  confirmPassword: "",
});
const isEditing = ref(false);
const total = ref(1);
const perPage = ref(10);
const Page = ref(1);
const list = ref([]);

const columns = computed(() => [
  {
    title: t("no."),
    align: "center",
    key: "index",
    render: (row, index) => {
      return perPage.value * (Page.value - 1) + index + 1;
    },
  },
  {
    title: t("username"),
    key: "username",
    align: "center",
    render: (row) => {
      return <div class="text-sm font-mono">{row.username || "-"}</div>;
    },
  },
  {
    title: t("position"),
    key: "position_type",
    align: "center",
    render: (row) => {
      return <n-tag type={row.position_type == 1 ? "error" : "info"}>{row.position_type == 1 ? t("admin") : t("member")}</n-tag>;
    },
  },
  {
    title: t("lastlogin"),
    key: "last_login",
    align: "center",
    render: (row) => {
      return row.last_login ? (
        <div class="text-sm">
          {moment(row.last_login)
            .tz("Asia/Bangkok")
            .format("DD/MM/YYYY HH:mm:ss")}
        </div>
      ) : (
        "-"
      );
    },
  },
  {
    title: t("lastip"),
    key: "last_ip",
    align: "center",
    render: (row) => {
      return <div class="text-sm font-mono">{row.last_ip || "-"}</div>;
    },
  },
  {
    title: t("createdate"),
    key: "created_at",
    align: "center",
    render: (row) => {
      return row.created_at ? (
        <div class="text-sm">
          {moment(row.created_at)
            .tz("Asia/Bangkok")
            .format("DD/MM/YYYY HH:mm:ss")}
        </div>
      ) : (
        "-"
      );
    },
  },
  {
    title: t("manage"),
    align: "center",
    key: "actions",
    render: (row) => {
      return (
        <n-space justify="center">
          <n-button
            type="primary"
            size="small"
            onClick={() => handleEditUser(row)}
          >
            <div class="flex items-center gap-1">
              <icon-park-outline-edit /> {t("edit")}
            </div>
          </n-button>
          <n-button
            type="error"
            size="small"
            onClick={() => handleDeleteUser(row)}
          >
            <div class="flex items-center gap-1">
              <icon-park-outline-delete /> {t("delete")}
            </div>
          </n-button>
        </n-space>
      );
    },
  },
]);

const handleAddEmployee = () => {
  addForm.value = {
    username: "",
    password: "",
    confirmPassword: "",
  };
  showAddModal.value = true;
};

const handleEditUser = (item) => {
 itemsedit.value = { ...item, password: "", confirmPassword: "" };
  isEditing.value = true;
  showModal.value = true;
};

const handleDeleteUser = (item) => {
  dialog.warning({
    title: t("confirmdelete"),
    content: t("areyousuredelete"),
    positiveText: t("delete"),
    negativeText: t("cancel"),
    onPositiveClick: () => {
      deleteUser(item);
    },
  });
};

const deleteUser = async (item) => {
  try {
    const obj = {
      id: item.id,
    };
    const response = await http.post(`v1/employee/deleteEmployee`, obj);
    if (response.data.success) {
      message.success(response.data.mes || t("deletesuccess"));
      getData();
    } else {
      message.error(response.data.mes || t("deletefailed"));
    }
  } catch (error) {
    message.error(t("deletefailed"));
  }
};

const saveUser = async () => {
      if (itemsedit.value.password && itemsedit.value.password !== itemsedit.value.confirmPassword) {
    message.error(t("validation.passwordMismatch"));
    return;
  }
  try {
    const obj = {
      id: itemsedit.value.id,
      username: itemsedit.value.username,
      password: itemsedit.value.password,
    };
    const response = await http.post(`v1/employee/storeEmployee`, obj);

    if (response.data.success) {
      message.success(response.data.mes || t("updatesuccess"));
      showModal.value = false;
      getData();
    } else {
      message.error(response.data.mes || t("updatefailed"));
    }
  } catch (error) {
    message.error(t("updatefailed"));
  }
};

const addEmployee = async () => {
  if (addForm.value.password !== addForm.value.confirmPassword) {
    message.error(t("validation.passwordMismatch"));
    return;
  }
  
  try {
    const obj = {
      username: addForm.value.username,
      password: addForm.value.password,
    };
    const response = await http.post(`v1/employee/storeEmployee`, obj);

    if (response.data.success) {
      message.success(response.data.mes || t("addSuccess"));
      showAddModal.value = false;
      getData();
    } else {
      message.error(response.data.mes || t("addFailed"));
    }
  } catch (error) {
    message.error(t("addFailed"));
  }
};

const getData = async () => {
  startLoading();
  try {
    const params = {
      perPage: perPage.value,
      page: Page.value,
      username: model.value.username,
    };
    const response = await http.get("v1/employee/getEmployee", { params });
    list.value = response.data.data || [];
    total.value = response.data.total || 1;
  } catch (error) {
    console.error("Error fetching employees:", error);
    message.error(t("fetchfailed"));
  } finally {
    endLoading();
  }
};

const changePage = (page, size) => {
  perPage.value = size;
  Page.value = page;
  getData();
};

onMounted(() => {
  getData();
});
</script>

<template>
  <div>
    <n-card>
      <n-space vertical size="large">
        <!-- Header Section -->
        <n-card>
          <n-space justify="space-between" align="center">
            <n-space align="center">
              <n-avatar color="#1a8a93">
                <icon-park-outline-peoples />
              </n-avatar>
              <div>
                <p class="text-lg font-medium">{{ $t("employeelist") }}</p>
                <n-text depth="3">{{ $t("manageEmployees") }}</n-text>
              </div>
            </n-space>
            <n-button type="primary" @click="handleAddEmployee">
              <template #icon>
                <icon-park-outline-plus />
              </template>
              {{ t("addemployee") }}
            </n-button>
          </n-space>
        </n-card>

        <!-- Search Form -->
        <n-card>
          <n-form
            ref="formRef"
            :model="model"
            :label-placement="isMobile ? 'top' : 'left'"
            :show-feedback="false"
          >
            <n-grid cols="1 600:2 1000:4" :x-gap="16" :y-gap="16">
              <n-gi>
                <n-form-item :label="$t('username')" path="username">
                  <n-input
                    v-model:value="model.username"
                    :placeholder="$t('enterUsername')"
                    clearable
                  >
                    <template #prefix>
                      <icon-park-outline-user />
                    </template>
                  </n-input>
                </n-form-item>
              </n-gi>

              <n-gi>
                <n-button type="default" @click="getData" block>
                  <template #icon>
                    <icon-park-outline-search />
                  </template>
                  {{ $t("search") }}
                </n-button>
              </n-gi>
              <n-gi>
                <n-button
                  type="default"
                  @click="model = { username: '' }"
                  block
                  secondary
                >
                  <template #icon>
                    <icon-park-outline-refresh />
                  </template>
                  {{ $t("reset") }}
                </n-button>
              </n-gi>
            </n-grid>
          </n-form>
        </n-card>

        <!-- Data Table -->
        <n-data-table
          :scroll-x="1200"
          :columns="columns"
          :data="list"
          :loading="loading"
        />

        <Pagination :count="total" @change="changePage" class="float-right" />
      </n-space>
    </n-card>

    <!-- Add Employee Modal -->
    <n-modal v-model:show="showAddModal" preset="dialog" :show-icon="false">
      <template #header>
        <n-space align="center">
          <icon-park-outline-plus />
          {{ $t("addemployee") }}
        </n-space>
      </template>

      <n-form :model="addForm" label-placement="left" class="mt-10">
        <n-form-item :label="$t('username')" path="username" required>
          <n-input
            v-model:value="addForm.username"
            :placeholder="$t('enterUsername')"
          />
        </n-form-item>
        <n-form-item :label="$t('password')" path="password" required>
          <n-input
            v-model:value="addForm.password"
            type="password"
            :placeholder="$t('enterPassword')"
          />
        </n-form-item>
        <n-form-item :label="$t('confirmpassword')" path="confirmPassword" required>
          <n-input
            v-model:value="addForm.confirmPassword"
            type="password"
            :placeholder="$t('confirmpassword')"
          />
        </n-form-item>
      </n-form>

      <template #action>
        <n-space justify="end">
          <n-button @click="showAddModal = false">
            {{ $t("cancel") }}
          </n-button>
          <n-button type="primary" @click="addEmployee">
            {{ $t("add") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- Edit Employee Modal -->
    <n-modal v-model:show="showModal" preset="dialog" :show-icon="false">
      <template #header>
        <n-space align="center">
          <icon-park-outline-edit />
          {{ $t("edit") }}
        </n-space>
      </template>

      <n-form :model="itemsedit" label-placement="left">
        <n-form-item :label="$t('username')" path="username" required class="mt-10">
          <n-input
            v-model:value="itemsedit.username"
            :placeholder="$t('enterUsername')"
          />
        </n-form-item>
     <n-form-item :label="$t('newpassword')" path="password">
          <n-input
            v-model:value="itemsedit.password"
            type="password"
            :placeholder="$t('enterNewPassword')"
          />
        </n-form-item>
        <n-form-item :label="$t('confirmpassword')" path="confirmPassword">
          <n-input
            v-model:value="itemsedit.confirmPassword"
            type="password"
            :placeholder="$t('confirmpassword')"
          />
        </n-form-item>
        
      </n-form>

      <template #action>
        <n-space justify="end">
          <n-button @click="showModal = false">
            {{ $t("cancel") }}
          </n-button>
          <n-button type="primary" @click="saveUser">
            {{ $t("save") }}
          </n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>
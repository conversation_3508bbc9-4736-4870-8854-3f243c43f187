import{d as b,y as P,D as V,r as p,s as B,b as C,f as e,w as n,j as r,t as s,i as l,G as E,B as T,fo as N}from"./index-DSmp6iCg.js";import{a as j}from"./headers-DsTJCVw-.js";import{_ as z,a as D}from"./FormItem-Be7I1W2B.js";import{_ as I}from"./Input-D61U907N.js";import{_ as q}from"./text-Df11blX7.js";const F=b({__name:"index",emits:["update:modelValue"],setup(A,{emit:m}){const f=m;function d(){f("update:modelValue","login")}const{t:g}=P(),h=V(()=>({account:{required:!0,trigger:"input",validator:(t,o)=>o?/^\d+$/.test(o)?/^[0-9]{10}$/.test(o)?!0:new Error("เบอร์โทรต้องยาว 10 ตัวเท่านั้น"):new Error("เบอร์โทรต้องเป็นเลขเท่านั้น"):new Error(g("login.resetPasswordRuleTip"))}})),a=p({account:""}),u=p(null);function w(){u.value?.validate()}return(t,o)=>{const _=j,y=I,c=D,i=T,v=q,x=N,$=E,k=z;return C(),B("div",null,[e(_,{depth:"3",class:"text-center"},{default:n(()=>[r(s(t.$t("login.resetPasswordTitle")),1)]),_:1}),e(k,{ref_key:"formRef",ref:u,rules:l(h),model:l(a),"show-label":!1,size:"large"},{default:n(()=>[e(c,{path:"account"},{default:n(()=>[e(y,{value:l(a).account,"onUpdate:value":o[0]||(o[0]=R=>l(a).account=R),clearable:"",placeholder:t.$t("login.resetPasswordPlaceholder")},null,8,["value","placeholder"])]),_:1}),e(c,null,{default:n(()=>[e($,{vertical:"",size:20,class:"w-full"},{default:n(()=>[e(i,{block:"",type:"primary",onClick:w},{default:n(()=>[r(s(t.$t("login.resetPassword")),1)]),_:1}),e(x,{justify:"center"},{default:n(()=>[e(v,null,{default:n(()=>[r(s(t.$t("login.haveAccountText")),1)]),_:1}),e(i,{text:"",type:"primary",onClick:d},{default:n(()=>[r(s(t.$t("login.signIn")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["rules","model"])])}}});export{F as _};

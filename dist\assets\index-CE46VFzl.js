import{_ as Ke}from"./delete-CCTWGk6A.js";import{_ as <PERSON>}from"./plus-CxnlDpiD.js";import{_ as He,a as We}from"./send-DLYXsI6z.js";import{Z as b,fp as x,$ as V,d as _e,a0 as De,a2 as Ee,fB as Ne,x as f,s as p,b as d,e,y as Ze,z as Ge,fk as Te,r as k,o as Ve,H as Z,D as Oe,v as $,f as t,i,w as s,K as Ye,fe as Je,t as l,E as Fe,j as u,B as Qe,J as Xe,c as j,fC as et,O as se,P as oe,G as tt,l as B,R as st,fm as ot,fo as nt}from"./index-DSmp6iCg.js";import{_ as lt}from"./setting-DFchZnlT.js";import{_ as rt}from"./lock-CFXZdX3i.js";import{_ as it}from"./edit-DdCXKTed.js";import{_ as at}from"./dollar-B-mq68Qd.js";import{m as dt}from"./index-BqRpacQ4.js";import{_ as ct}from"./text-Df11blX7.js";import{_ as ut,a as pt}from"./FormItem-Be7I1W2B.js";import{_ as _t}from"./Input-D61U907N.js";import"./moment-zH0z38ay.js";const mt=b("input-group",`
 display: inline-flex;
 width: 100%;
 flex-wrap: nowrap;
 vertical-align: bottom;
`,[x(">",[b("input",[x("&:not(:last-child)",`
 border-top-right-radius: 0!important;
 border-bottom-right-radius: 0!important;
 `),x("&:not(:first-child)",`
 border-top-left-radius: 0!important;
 border-bottom-left-radius: 0!important;
 margin-left: -1px!important;
 `)]),b("button",[x("&:not(:last-child)",`
 border-top-right-radius: 0!important;
 border-bottom-right-radius: 0!important;
 `,[V("state-border, border",`
 border-top-right-radius: 0!important;
 border-bottom-right-radius: 0!important;
 `)]),x("&:not(:first-child)",`
 border-top-left-radius: 0!important;
 border-bottom-left-radius: 0!important;
 `,[V("state-border, border",`
 border-top-left-radius: 0!important;
 border-bottom-left-radius: 0!important;
 `)])]),x("*",[x("&:not(:last-child)",`
 border-top-right-radius: 0!important;
 border-bottom-right-radius: 0!important;
 `,[x(">",[b("input",`
 border-top-right-radius: 0!important;
 border-bottom-right-radius: 0!important;
 `),b("base-selection",[b("base-selection-label",`
 border-top-right-radius: 0!important;
 border-bottom-right-radius: 0!important;
 `),b("base-selection-tags",`
 border-top-right-radius: 0!important;
 border-bottom-right-radius: 0!important;
 `),V("box-shadow, border, state-border",`
 border-top-right-radius: 0!important;
 border-bottom-right-radius: 0!important;
 `)])])]),x("&:not(:first-child)",`
 margin-left: -1px!important;
 border-top-left-radius: 0!important;
 border-bottom-left-radius: 0!important;
 `,[x(">",[b("input",`
 border-top-left-radius: 0!important;
 border-bottom-left-radius: 0!important;
 `),b("base-selection",[b("base-selection-label",`
 border-top-left-radius: 0!important;
 border-bottom-left-radius: 0!important;
 `),b("base-selection-tags",`
 border-top-left-radius: 0!important;
 border-bottom-left-radius: 0!important;
 `),V("box-shadow, border, state-border",`
 border-top-left-radius: 0!important;
 border-bottom-left-radius: 0!important;
 `)])])])])])]),gt={},ft=_e({name:"InputGroup",props:gt,setup(m){const{mergedClsPrefixRef:r}=Ee(m);return Ne("-input-group",mt,r),{mergedClsPrefix:r}},render(){const{mergedClsPrefix:m}=this;return De("div",{class:`${m}-input-group`},this.$slots)}}),ht={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function kt(m,r){return d(),p("svg",ht,r[0]||(r[0]=[e("path",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4",d:"M19 4h18L26 18h15L17 44l5-19H8z"},null,-1)]))}const yt=f({name:"icon-park-outline-lightning",render:kt}),vt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function bt(m,r){return d(),p("svg",vt,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-width":"4"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19 32h10v9H19z"}),e("rect",{width:"38",height:"24",x:"5",y:"8",rx:"2"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M22 27h4M14 41h20"})],-1)]))}const wt=f({name:"icon-park-outline-computer",render:bt}),xt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function $t(m,r){return d(),p("svg",xt,r[0]||(r[0]=[e("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4",d:"m5 10l3 3l6-6M5 24l3 3l6-6M5 38l3 3l6-6m7-11h22M21 38h22M21 10h22"},null,-1)]))}const Ct=f({name:"icon-park-outline-list",render:$t}),jt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Mt(m,r){return d(),p("svg",jt,r[0]||(r[0]=[e("path",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4",d:"M6 8.256L24.009 3L42 8.256v10.778A26.32 26.32 0 0 1 24.003 44A26.32 26.32 0 0 1 6 19.029z"},null,-1)]))}const Pt=f({name:"icon-park-outline-shield",render:Mt}),zt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Bt(m,r){return d(),p("svg",zt,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M4 24c0 11.046 8.954 20 20 20v0c11.046 0 20-8.954 20-20S35.046 4 24 4"}),e("path",{d:"M36 24c0-6.627-5.373-12-12-12s-12 5.373-12 12s5.373 12 12 12v0"})],-1)]))}const At=f({name:"icon-park-outline-loading",render:Bt}),St={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Ut(m,r){return d(),p("svg",St,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M22.868 24.298a9.87 9.87 0 0 1 2.63 9.588a9.93 9.93 0 0 1-7.065 7.028a9.99 9.99 0 0 1-9.64-2.615a9.863 9.863 0 0 1 .122-13.878c3.839-3.82 10.046-3.873 13.951-.121z"}),e("path",{"stroke-linecap":"round",d:"M23 24L40 7"}),e("path",{d:"m30.305 16.9l5.429 5.4l6.333-6.3l-5.428-5.4z"})],-1)]))}const It=f({name:"icon-park-outline-key",render:Ut}),qt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Rt(m,r){return d(),p("svg",qt,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{"stroke-linecap":"round",d:"M13 12.432v-4.62A2.813 2.813 0 0 1 15.813 5h24.374A2.813 2.813 0 0 1 43 7.813v24.375A2.813 2.813 0 0 1 40.188 35h-4.672"}),e("path",{d:"M32.188 13H7.811A2.813 2.813 0 0 0 5 15.813v24.374A2.813 2.813 0 0 0 7.813 43h24.375A2.813 2.813 0 0 0 35 40.188V15.811A2.813 2.813 0 0 0 32.188 13Z"})],-1)]))}const Kt=f({name:"icon-park-outline-copy",render:Rt}),Lt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Ht(m,r){return d(),p("svg",Lt,r[0]||(r[0]=[e("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4",d:"M24.707 9.565L9.858 24.415a9 9 0 0 0 0 12.727v0a9 9 0 0 0 12.728 0l17.678-17.677a6 6 0 0 0 0-8.486v0a6 6 0 0 0-8.486 0L14.101 28.657a3 3 0 0 0 0 4.243v0a3 3 0 0 0 4.242 0l14.85-14.85"},null,-1)]))}const Wt=f({name:"icon-park-outline-link",render:Ht}),Dt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Et(m,r){return d(),p("svg",Dt,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M38.512 9.536A20.43 20.43 0 0 0 24.5 4C13.178 4 4 13.178 4 24.5S13.178 45 24.5 45a20.43 20.43 0 0 0 14.405-5.914L24 24z"}),e("path",{d:"M40 28a4 4 0 1 0 0-8a4 4 0 0 0 0 8Z"}),e("path",{"stroke-linecap":"round",d:"M17 13v8m-4-4h8"})],-1)]))}const Nt=f({name:"icon-park-outline-game",render:Et}),Zt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Gt(m,r){return d(),p("svg",Zt,r[0]||(r[0]=[e("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4",d:"m37 22l-3 3l-11-11l3-3c1.5-1.5 7-4 11 0s1.5 9.5 0 11m5-16l-5 5M11 26l3-3l11 11l-3 3c-1.5 1.5-7 4-11 0s-1.5-9.5 0-11m12 6l4-4M6 42l5-5m5-12l4-4"},null,-1)]))}const Tt=f({name:"icon-park-outline-api",render:Gt}),Vt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Ot(m,r){return d(),p("svg",Vt,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4S4 12.954 4 24s8.954 20 20 20Z"}),e("path",{"stroke-linecap":"round",d:"M29.657 18.343L18.343 29.657m0-11.314l11.314 11.314"})],-1)]))}const Yt=f({name:"icon-park-outline-close-one",render:Ot}),Jt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Ft(m,r){return d(),p("svg",Jt,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M24 44a19.94 19.94 0 0 0 14.142-5.858A19.94 19.94 0 0 0 44 24a19.94 19.94 0 0 0-5.858-14.142A19.94 19.94 0 0 0 24 4A19.94 19.94 0 0 0 9.858 9.858A19.94 19.94 0 0 0 4 24a19.94 19.94 0 0 0 5.858 14.142A19.94 19.94 0 0 0 24 44Z"}),e("path",{"stroke-linecap":"round",d:"m16 24l6 6l12-12"})],-1)]))}const Qt=f({name:"icon-park-outline-check-one",render:Ft}),Xt={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function es(m,r){return d(),p("svg",Xt,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M23.992 6H6v36h18"}),e("path",{d:"m25 33l-9-9l9-9m17 8.992H16"})],-1)]))}const ts=f({name:"icon-park-outline-login",render:es}),ss={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function os(m,r){return d(),p("svg",ss,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4S4 12.954 4 24s8.954 20 20 20Z"}),e("path",{"stroke-linecap":"round",d:"M24.008 12v12.01l8.479 8.48"})],-1)]))}const ns=f({name:"icon-park-outline-time",render:os}),ls={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function rs(m,r){return d(),p("svg",ls,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"4"},[e("path",{d:"M42 8H6a2 2 0 0 0-2 2v28a2 2 0 0 0 2 2h36a2 2 0 0 0 2-2V10a2 2 0 0 0-2-2Z"}),e("path",{d:"M36 16h-8v8h8z"}),e("path",{"stroke-linecap":"round",d:"M12 32h24M12 16h6m-6 8h6"})],-1)]))}const is=f({name:"icon-park-outline-id-card",render:rs}),as={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function ds(m,r){return d(),p("svg",as,r[0]||(r[0]=[e("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[e("circle",{cx:"24",cy:"11",r:"7"}),e("path",{d:"M4 41c0-8.837 8.059-16 18-16m9 17l10-10l-4-4l-10 10v4z"})],-1)]))}const cs=f({name:"icon-park-outline-edit-name",render:ds}),us={key:0,class:"max-w-7xl mx-auto"},ps={class:"mb-4"},_s={class:"flex flex-col sm:flex-row items-center justify-between"},ms={class:"flex items-center space-x-4 mb-4 sm:mb-0"},gs={class:"text-2xl font-bold"},fs={class:"text-gray-600 dark:text-gray-400"},hs={class:"flex items-center space-x-2"},ks={class:"grid grid-cols-1 xl:grid-cols-3 gap-4"},ys={class:"xl:col-span-2 space-y-4"},vs={class:"flex items-center space-x-2"},bs={class:"font-semibold"},ws={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},xs={class:"flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"},$s={class:"flex-1"},Cs={class:"text-sm text-gray-600 dark:text-gray-400"},js={class:"flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"},Ms={class:"flex-1"},Ps={class:"text-sm text-gray-600 dark:text-gray-400"},zs={class:"flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"},Bs={class:"flex-1"},As={class:"text-sm text-gray-600 dark:text-gray-400"},Ss={class:"flex items-center space-x-3 p-4 bg-gray-50 dark:bg-gray-800/20 rounded-lg"},Us={class:"flex-1"},Is={class:"text-sm text-gray-600 dark:text-gray-400"},qs={class:"flex items-center space-x-2"},Rs={class:"font-semibold"},Ks={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"},Ls={class:"flex items-center space-x-3"},Hs=["src"],Ws={class:"font-medium"},Ds={class:"text-sm text-gray-600 dark:text-gray-400"},Es={class:"flex items-center space-x-2"},Ns={class:"font-semibold"},Zs={class:"space-y-4"},Gs={class:"flex items-center justify-between p-4 dark:bg-gray-800/20 rounded-lg"},Ts={class:"flex items-center space-x-3"},Vs={class:"font-medium"},Os={class:"p-4 dark:bg-gray-800/20 rounded-lg"},Ys={class:"flex items-center justify-between mb-2"},Js={class:"flex items-center space-x-3"},Fs={class:"p-4 dark:bg-gray-800/20 rounded-lg"},Qs={class:"flex items-center justify-between mb-2"},Xs={class:"flex items-center space-x-3"},eo={class:"p-4 dark:bg-gray-800/20 rounded-lg"},to={class:"flex items-center justify-between mb-2"},so={class:"flex items-center space-x-3"},oo={class:"space-y-4"},no={class:"flex items-center space-x-2"},lo={class:"font-semibold"},ro={class:"space-y-4"},io={class:"flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg"},ao={class:"flex items-center space-x-3"},co={class:"font-medium"},uo={class:"text-sm text-gray-600 dark:text-gray-400"},po={key:0,class:"flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg"},_o={class:"flex items-center space-x-3"},mo={class:"text-sm text-gray-600 dark:text-gray-400"},go={class:"flex items-center space-x-2"},fo={class:"font-semibold"},ho={class:"space-y-2"},ko={class:"flex items-center space-x-3"},yo={class:"flex items-center space-x-2"},vo={class:"font-semibold"},bo={class:"space-y-3"},wo={class:"flex items-center space-x-2"},xo={class:"font-semibold"},$o={class:"space-y-4"},Co={class:"text-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg"},jo={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Mo={class:"text-sm text-gray-600 dark:text-gray-400"},Po={key:0,class:"text-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg"},zo={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Bo={class:"text-sm text-gray-600 dark:text-gray-400"},Ao={key:1,class:"text-center p-4 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg"},So={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},Uo={class:"text-sm text-gray-600 dark:text-gray-400"},Io={class:"flex items-center gap-2"},qo={class:"flex items-center gap-2"},Ro={class:"flex items-center gap-2"},Ko={key:0,class:"space-y-4"},Lo={class:"flex items-center justify-between"},Ho={class:"flex items-center gap-2"},Wo={class:"font-medium text-gray-700 dark:text-gray-300"},Do={class:"grid grid-cols-1 gap-3 max-h-60 overflow-y-auto"},Eo={class:"flex items-center gap-3"},No={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Zo={class:"flex items-center gap-2"},Go={key:1,class:"text-center py-8"},To={class:"flex flex-col items-center gap-3"},Vo={class:"text-gray-500 dark:text-gray-400 font-medium"},Oo={class:"text-sm text-gray-400 dark:text-gray-500 mt-1"},Yo={class:"mt-5"},Jo={class:"text-center"},Fo={class:"text-center mb-5"},Qo={class:"ml-1"},_n=_e({__name:"index",setup(m){const{t:r}=Ze(),v=Ge(),{userData:M}=Te(),A=()=>!((M?.position_type==4&&M?.upline_position_type==3||M?.position_type==4&&M?.upline_position_type==2||M?.position_type==4&&M?.upline_position_type==1)&&JSON.parse(M?.permissions).management!==2);k([]);const me=k([]),c=k(null);Ve(()=>{S()});const P=k([]),S=async()=>{const{data:n}=await Z.get("v1/Profile/agent");if(c.value=n.data,c.value.whitelist_ip.length&&(me.value=n.data.whitelist_ip[0]),P.value=[],n.data.position_type==3){const o={currency:"THB",images:"/images/country/th.webp",hold_percent:n.data.hold_percent};P.value.push(o)}else{if(n.data.reseller_thb){const o={currency:"THB",images:"/images/country/th.webp",hold_percent:n.data.hold_percent};P.value.push(o)}if(n.data.reseller_krw){const o={currency:"KRW",images:"/images/country/kr.webp",hold_percent:n.data.hold_percent_krw};P.value.push(o)}if(n.data.reseller_usd){const o={currency:"USD",images:"/images/country/us.webp",hold_percent:n.data.hold_percent_usd};P.value.push(o)}}},U=k(!1),I=k(!1),q=k(!1),L=k(!1),R=k(null),H=k(null),w=k(null),y=k([]),g=k({oldpassword:null,newpassword:null,confirmpassword:null}),ge=Oe(()=>({oldpassword:[{required:!0,message:r("validation.oldPasswordRequired"),trigger:"blur"}],newpassword:[{required:!0,message:r("validation.newPasswordRequired"),trigger:"blur"}],confirmpassword:[{required:!0,message:r("validation.confirmPasswordRequired"),trigger:"blur"}],whitlistip:[{required:!0,message:r("validation.whitelistIPRequired"),trigger:"blur"}],callbackUrl:[{required:!0,message:r("validation.callbackURLRequired"),trigger:"blur"}]})),ne=n=>dt(n).tz("Asia/Bangkok").format("DD/MM/YYYY HH:mm"),O=()=>{g.value={oldpassword:null,newpassword:null,confirmpassword:null},U.value=!0},fe=()=>{if(!g.value.oldpassword||!g.value.newpassword||!g.value.confirmpassword)return v.error(r("validation.passwordRequired"));if(g.value.newpassword!=g.value.confirmpassword)return v.error(r("validation.passwordMismatch"));const n={old_pass:g.value.oldpassword,new_pass:g.value.confirmpassword,password:g.value.newpassword};Z.post("v1/changepass",n).then(o=>{o.data.success?(v.success(o.data.mes),S(),U.value=!1):v.error(o.data.mes)})},le=()=>{R.value=null,L.value=!1,Z.post("v1/Store/SecretKey").then(n=>{n.data.success?(v.success(r("secretKeyGeneratedSuccess")),R.value=n.data.mes,L.value=!0,S()):v.error(r("secretKeyGenerationError"))})},re=()=>{H.value=c.value.callback_url,q.value=!0},he=()=>{const n={CallbackUrl:H.value};Z.post("v1/Update/callbackurl",n).then(o=>{o.data.success?(v.success(r("saveDataSuccess")),S(),q.value=!1):v.error(r("errorOccurred"))})},Y=n=>{n?(y.value=c.value.whitelist_ip,I.value=!0):(I.value=!1,y.value=[],w.value=null,S())},ie=()=>{w.value&&!y.value.includes(w.value)&&y.value.unshift(w.value)},ke=()=>{const n={whitelist_ip:y.value};Z.post("v1/Update/change_whitelist_ip",n).then(o=>{o.data.success?(v.success(r("saveDataSuccess")),S(),I.value=!1,y.value=[],w.value=null):v.error(r("errorOccurred"))})},J=n=>{n&&navigator.clipboard.writeText(n).then(()=>{v.success(r("copysuccess"))})};return(n,o)=>{const ye=Je,h=Ye,W=Fe,ve=cs,_=Qe,C=Xe,ae=is,be=ns,z=ct,we=ts,xe=Qt,$e=Yt,Ce=at,je=Tt,Me=Nt,Pe=Wt,F=Kt,Q=It,ze=At,D=tt,X=He,de=it,E=Pt,ee=rt,Be=lt,ce=Ct,ue=wt,Ae=yt,Se=We,K=_t,N=pt,te=ut,G=st,Ue=Le,Ie=ft,pe=Ke,qe=nt;return d(),p("div",null,[i(c)?(d(),p("div",us,[e("div",ps,[t(C,null,{default:s(()=>[e("div",_s,[e("div",ms,[t(h,{size:"large",style:{backgroundColor:"#3b82f6"}},{default:s(()=>[t(ye,{class:"text-2xl"})]),_:1}),e("div",null,[e("h1",gs,l(i(c).username),1),e("p",fs,l(i(c).position_type==1?n.$t("company"):i(c).position_type==2?n.$t("reseller"):n.$t("agent")),1)])]),e("div",hs,[t(W,{type:i(c).status==1?"success":"error",size:"large"},{default:s(()=>[u(l(n.$t(i(c).status==1?"active":"inactive")),1)]),_:1},8,["type"]),t(_,{onClick:o[0]||(o[0]=a=>O()),type:"primary",size:"medium"},{icon:s(()=>[t(ve)]),default:s(()=>[u(" "+l(n.$t("common.edit")),1)]),_:1})])])]),_:1})]),e("div",ks,[e("div",ys,[t(C,null,{header:s(()=>[e("div",vs,[t(ae,{class:"text-lg"}),e("span",bs,l(n.$t("basicinfomation")),1)])]),default:s(()=>[e("div",ws,[e("div",xs,[t(h,{style:{backgroundColor:"#8b5cf6"}},{default:s(()=>[t(ae,{class:"text-xl"})]),_:1}),e("div",$s,[e("p",Cs,l(n.$t("accounttype")),1),t(W,{size:"small",type:i(c).position_type==1?"error":i(c).position_type==2?"warning":"success"},{default:s(()=>[u(l(i(c).position_type==1?n.$t("company"):i(c).position_type==2?n.$t("reseller"):n.$t("agent")),1)]),_:1},8,["type"])])]),e("div",js,[t(h,{style:{backgroundColor:"#78350f"}},{default:s(()=>[t(be,{class:"text-xl"})]),_:1}),e("div",Ms,[e("p",Ps,l(n.$t("createtime")),1),t(z,{code:""},{default:s(()=>[u(l(ne(i(c).created_at)),1)]),_:1})])]),e("div",zs,[t(h,{style:{backgroundColor:"#000000"}},{default:s(()=>[t(we,{class:"text-xl"})]),_:1}),e("div",Bs,[e("p",As,l(n.$t("lastlogin")),1),t(z,{code:""},{default:s(()=>[u(l(ne(i(c).last_login)),1)]),_:1})])]),e("div",Ss,[t(h,{style:et({backgroundColor:i(c).status==1?"#10b981":"#ef4444"})},{default:s(()=>[i(c).status==1?(d(),j(xe,{key:0,class:"text-xl"})):(d(),j($e,{key:1,class:"text-xl"}))]),_:1},8,["style"]),e("div",Us,[e("p",Is,l(n.$t("status")),1),t(W,{size:"small",type:i(c).status==1?"success":"error"},{default:s(()=>[u(l(n.$t(i(c).status==1?"active":"inactive")),1)]),_:1},8,["type"])])])])]),_:1}),t(C,null,{header:s(()=>[e("div",qs,[t(Ce,{class:"text-lg"}),e("span",Rs,l(n.$t("currencysupported")),1)])]),default:s(()=>[e("div",Ks,[(d(!0),p(se,null,oe(i(P),a=>(d(),p("div",{key:a.currency,class:"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700/30 rounded-lg"},[e("div",Ls,[e("img",{src:a.images,width:"32",height:"32",class:"rounded-sm"},null,8,Hs),e("div",null,[e("p",Ws,l(a.currency),1),e("p",Ds,l(n.$t("hold"))+" : "+l(a.hold_percent)+"% ",1)])]),t(W,{type:"warning"},{default:s(()=>[u(l(a.hold_percent)+"% ",1)]),_:2},1024)]))),128))])]),_:1}),i(c).position_type==3?(d(),j(C,{key:0},{header:s(()=>[e("div",Es,[t(je,{class:"text-lg"}),e("span",Ns,l(n.$t("API-Configuration")),1)])]),default:s(()=>[e("div",Zs,[e("div",Gs,[e("div",Ts,[t(h,{style:{backgroundColor:"#10b981"}},{default:s(()=>[t(Me,{class:"text-xl"})]),_:1}),e("div",null,[e("p",Vs,l(n.$t("bettype")),1)])]),t(W,{type:"info"},{default:s(()=>[u(l(i(c).bet_type==1?"Seamless":"Transfer"),1)]),_:1})]),e("div",Os,[e("div",Ys,[e("div",Js,[t(h,{style:{backgroundColor:"#3b82f6"}},{default:s(()=>[t(Pe,{class:"text-xl"})]),_:1}),o[30]||(o[30]=e("p",{class:"font-medium"},"API Endpoint",-1))]),t(_,{type:"default",secondary:"",size:"small",onClick:o[1]||(o[1]=a=>J("https://api.pgf-theks69.com"))},{icon:s(()=>[t(F)]),default:s(()=>[u(" "+l(n.$t("copy")),1)]),_:1})]),t(z,{code:"",class:"break-all"},{default:s(()=>o[31]||(o[31]=[u("https://api.pgf-theks69.com")])),_:1})]),e("div",Fs,[e("div",Qs,[e("div",Xs,[t(h,{style:{backgroundColor:"#f59e0b"}},{default:s(()=>[t(Q,{class:"text-xl"})]),_:1}),o[32]||(o[32]=e("p",{class:"font-medium"},"Secret Key",-1))]),t(D,null,{default:s(()=>[t(_,{type:"warning",secondary:"",size:"small",onClick:o[2]||(o[2]=a=>le()),disabled:!A()},{icon:s(()=>[t(ze)]),default:s(()=>[u(" "+l(n.$t("generateapikey")),1)]),_:1},8,["disabled"])]),_:1})]),t(z,{code:"",class:"break-all"},{default:s(()=>[u(l(i(c).secret_key),1)]),_:1})]),e("div",eo,[e("div",to,[e("div",so,[t(h,{style:{backgroundColor:"#8b5cf6"}},{default:s(()=>[t(X,{class:"text-xl"})]),_:1}),o[33]||(o[33]=e("p",{class:"font-medium"},"Callback URL",-1))]),t(D,null,{default:s(()=>[t(_,{type:"primary",secondary:"",size:"small",onClick:o[3]||(o[3]=a=>re()),disabled:!A()},{icon:s(()=>[t(de)]),default:s(()=>[u(" "+l(n.$t("edit")),1)]),_:1},8,["disabled"])]),_:1})]),t(z,{code:"",class:"break-all"},{default:s(()=>[u(l(i(c).callback_url||"Not configured"),1)]),_:1})])])]),_:1})):$("",!0)]),e("div",oo,[t(C,null,{header:s(()=>[e("div",no,[t(E,{class:"text-lg"}),e("span",lo,l(n.$t("security-settings")),1)])]),default:s(()=>[e("div",ro,[e("div",io,[e("div",ao,[t(h,{style:{backgroundColor:"#ef4444"}},{default:s(()=>[t(ee,{class:"text-xl"})]),_:1}),e("div",null,[e("p",co,l(n.$t("password")),1),e("p",uo,l(n.$t("changepassword")),1)])]),t(_,{type:"error",secondary:"",size:"small",onClick:o[4]||(o[4]=a=>O())},{icon:s(()=>[t(de)]),default:s(()=>[u(" "+l(n.$t("edit")),1)]),_:1})]),i(c).position_type==3?(d(),p("div",po,[e("div",_o,[t(h,{style:{backgroundColor:"#f97316"}},{default:s(()=>[t(E,{class:"text-xl"})]),_:1}),e("div",null,[o[34]||(o[34]=e("p",{class:"font-medium"},"Whitelist IP",-1)),e("p",mo,l(i(c).whitelist_ip?.length||0)+" IP(s) "+l(n.$t("configured")),1)])]),t(_,{type:"warning",secondary:"",size:"small",onClick:o[5]||(o[5]=a=>Y(!0)),disabled:!A()},{icon:s(()=>[t(Be)]),default:s(()=>[u(" "+l(n.$t("manage")),1)]),_:1},8,["disabled"])])):$("",!0)])]),_:1}),i(c).position_type==3&&i(c).whitelist_ip?.length>0?(d(),j(C,{key:0},{header:s(()=>[e("div",go,[t(ce,{class:"text-lg"}),e("span",fo,l(n.$t("Current-Whitelist-IPs")),1)])]),default:s(()=>[e("div",ho,[(d(!0),p(se,null,oe(i(c).whitelist_ip,(a,T)=>(d(),p("div",{key:T,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/20 rounded-lg"},[e("div",ko,[t(h,{size:"small",style:{backgroundColor:"#10b981"}},{default:s(()=>[t(ue,{class:"text-sm"})]),_:1}),t(z,{code:""},{default:s(()=>[u(l(a),1)]),_:2},1024)])]))),128))])]),_:1})):$("",!0),t(C,null,{header:s(()=>[e("div",yo,[t(Ae,{class:"text-lg"}),e("span",vo,l(n.$t("quick-actions")),1)])]),default:s(()=>[e("div",bo,[t(_,{block:"",type:"default",onClick:o[6]||(o[6]=a=>O())},{icon:s(()=>[t(ee)]),default:s(()=>[u(" "+l(n.$t("editpassword")),1)]),_:1}),i(c).position_type==3?(d(),j(_,{key:0,block:"",type:"default",onClick:o[7]||(o[7]=a=>le()),disabled:!A()},{icon:s(()=>[t(Q)]),default:s(()=>[u(" "+l(n.$t("generateapikey")),1)]),_:1},8,["disabled"])):$("",!0),i(c).position_type==3?(d(),j(_,{key:1,block:"",type:"default",onClick:o[8]||(o[8]=a=>re()),disabled:!A()},{icon:s(()=>[t(X)]),default:s(()=>[u(" "+l(n.$t("editcallbackurl")),1)]),_:1},8,["disabled"])):$("",!0),i(c).position_type==3?(d(),j(_,{key:2,block:"",type:"default",onClick:o[9]||(o[9]=a=>Y(!0)),disabled:!A()},{icon:s(()=>[t(E)]),default:s(()=>[u(" "+l(n.$t("editwhitelistip")),1)]),_:1},8,["disabled"])):$("",!0)])]),_:1}),t(C,null,{header:s(()=>[e("div",wo,[t(Se,{class:"text-lg"}),e("span",xo,l(n.$t("account-statistics")),1)])]),default:s(()=>[e("div",$o,[e("div",Co,[e("div",jo,l(i(P).length),1),e("div",Mo,l(n.$t("currencysupported")),1)]),i(c).position_type==3?(d(),p("div",Po,[e("div",zo,l(i(c).whitelist_ip?.length||0),1),e("div",Bo,l(n.$t("Current-Whitelist-IPs")),1)])):$("",!0),i(c).position_type==3?(d(),p("div",Ao,[e("div",So,l(i(c).secret_key?"✓":"✗"),1),e("div",Uo,l(n.$t("API-Configuration")),1)])):$("",!0)])]),_:1})])])])):$("",!0),t(G,{show:i(U),"onUpdate:show":o[15]||(o[15]=a=>B(U)?U.value=a:null),preset:"dialog","show-icon":!1},{header:s(()=>[e("div",Io,[t(ee,{class:"text-[#f59e0b]"}),e("span",null,l(n.$t("changepassword")),1)])]),action:s(()=>[t(D,{justify:"end"},{default:s(()=>[t(_,{onClick:o[13]||(o[13]=a=>U.value=!1)},{default:s(()=>[u(l(n.$t("cancel")),1)]),_:1}),t(_,{type:"primary",onClick:o[14]||(o[14]=a=>fe())},{default:s(()=>[u(l(n.$t("save")),1)]),_:1})]),_:1})]),default:s(()=>[t(te,{ref:"formRef",model:i(g),rules:i(ge),"label-placement":"top"},{default:s(()=>[t(N,{label:n.$t("oldpassword"),path:"oldpassword"},{default:s(()=>[t(K,{value:i(g).oldpassword,"onUpdate:value":o[10]||(o[10]=a=>i(g).oldpassword=a),type:"password","show-password-on":"mousedown",placeholder:n.$t("oldpassword")},null,8,["value","placeholder"])]),_:1},8,["label"]),t(N,{label:n.$t("newpassword"),path:"newpassword"},{default:s(()=>[t(K,{value:i(g).newpassword,"onUpdate:value":o[11]||(o[11]=a=>i(g).newpassword=a),type:"password","show-password-on":"mousedown",placeholder:n.$t("newpassword")},null,8,["value","placeholder"])]),_:1},8,["label"]),t(N,{label:n.$t("confirmpassword"),path:"confirmpassword"},{default:s(()=>[t(K,{value:i(g).confirmpassword,"onUpdate:value":o[12]||(o[12]=a=>i(g).confirmpassword=a),type:"password","show-password-on":"mousedown",placeholder:n.$t("confirmpassword")},null,8,["value","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["show"]),t(G,{show:i(q),"onUpdate:show":o[19]||(o[19]=a=>B(q)?q.value=a:null),preset:"dialog","show-icon":!1},{header:s(()=>[e("div",qo,[t(X,{class:"text-[#8b5cf6]"}),e("span",null,l(n.$t("editcallbackurl")),1)])]),action:s(()=>[t(D,{justify:"end"},{default:s(()=>[t(_,{onClick:o[17]||(o[17]=a=>q.value=!1)},{default:s(()=>[u(l(n.$t("cancel")),1)]),_:1}),t(_,{type:"primary",onClick:o[18]||(o[18]=a=>he())},{default:s(()=>[u(l(n.$t("save")),1)]),_:1})]),_:1})]),default:s(()=>[t(te,null,{default:s(()=>[t(N,{path:"callbackUrl"},{default:s(()=>[t(K,{value:i(H),"onUpdate:value":o[16]||(o[16]=a=>B(H)?H.value=a:null),placeholder:n.$t("callbackurl"),clearable:""},null,8,["value","placeholder"])]),_:1})]),_:1})]),_:1},8,["show"]),t(G,{show:i(I),"onUpdate:show":o[26]||(o[26]=a=>B(I)?I.value=a:null),preset:"dialog","show-icon":!1},{header:s(()=>[e("div",Ro,[t(E,{class:"text-[#ef4444]"}),e("span",null,l(n.$t("editwhitelistip")),1)])]),action:s(()=>[t(D,{justify:"end"},{default:s(()=>[t(_,{onClick:o[24]||(o[24]=a=>Y(!1))},{default:s(()=>[u(l(n.$t("cancel")),1)]),_:1}),t(_,{type:"primary",onClick:o[25]||(o[25]=a=>ke())},{default:s(()=>[u(l(n.$t("save")),1)]),_:1})]),_:1})]),default:s(()=>[e("div",null,[e("div",null,[t(te,{"label-placement":"top"},{default:s(()=>[t(N,{path:"whitlistip"},{default:s(()=>[t(Ie,null,{default:s(()=>[t(K,{value:i(w),"onUpdate:value":o[20]||(o[20]=a=>B(w)?w.value=a:null),placeholder:n.$t("ipAddressPlaceholder"),onKeyup:o[21]||(o[21]=ot(a=>ie(),["enter"])),size:"large",clearable:""},null,8,["value","placeholder"]),t(_,{type:"primary",onClick:o[22]||(o[22]=a=>ie()),size:"large",disabled:!i(w)||!i(w).trim()},{icon:s(()=>[t(Ue)]),default:s(()=>[u(" "+l(n.$t("add")),1)]),_:1},8,["disabled"])]),_:1})]),_:1})]),_:1}),o[35]||(o[35]=e("div",{class:"flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400"},null,-1))]),i(y).length>0?(d(),p("div",Ko,[e("div",Lo,[e("div",Ho,[t(ce,{class:"text-[#10b981]"}),e("span",Wo,l(n.$t("currentips"))+" ("+l(i(y).length)+") ",1)]),i(y).length>1?(d(),j(_,{key:0,type:"error",quaternary:"",size:"small",onClick:o[23]||(o[23]=a=>y.value=[])},{icon:s(()=>[t(pe)]),default:s(()=>[u(" "+l(n.$t("clearall")),1)]),_:1})):$("",!0)]),e("div",Do,[(d(!0),p(se,null,oe(i(y),(a,T)=>(d(),p("div",{key:T,class:"flex items-center justify-between p-3 bg-white dark:bg-gray-800/20 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow"},[e("div",Eo,[t(h,{size:"small",style:{backgroundColor:"#10b981"}},{default:s(()=>[t(ue,{class:"text-sm"})]),_:1}),e("div",null,[t(z,{code:"",class:"text-sm"},{default:s(()=>[u(l(a),1)]),_:2},1024),e("p",No,l(n.$t("addedip")||"Added IP Address"),1)])]),e("div",Zo,[t(_,{type:"default",quaternary:"",size:"small",onClick:Re=>J(a)},{icon:s(()=>[t(F)]),_:2},1032,["onClick"]),t(_,{type:"error",quaternary:"",size:"small",onClick:Re=>i(y).splice(T,1)},{icon:s(()=>[t(pe)]),_:2},1032,["onClick"])])]))),128))])])):(d(),p("div",Go,[e("div",To,[t(h,{size:"large",style:{backgroundColor:"#f3f4f6"}},{default:s(()=>[t(E,{class:"text-2xl text-gray-400"})]),_:1}),e("div",null,[e("p",Vo,l(n.$t("noipsadded")||"No IP addresses added yet"),1),e("p",Oo,l(n.$t("addiphelp")||"Add IP addresses to restrict access to your account"),1)])])]))])]),_:1},8,["show"]),t(G,{show:i(L),"onUpdate:show":o[29]||(o[29]=a=>B(L)?L.value=a:null),preset:"dialog","show-icon":!1},{header:s(()=>[t(Q),o[36]||(o[36]=e("span",{class:"ml-1"},"Secret Key",-1))]),default:s(()=>[e("div",Yo,[e("p",Jo,l(n.$t("secretkey1")),1),e("p",Fo,l(n.$t("secretkey2")),1),t(K,{value:i(R),"onUpdate:value":o[27]||(o[27]=a=>B(R)?R.value=a:null),placeholder:"",type:"text",disabled:""},null,8,["value"]),t(qe,{justify:"center",class:"mt-5"},{default:s(()=>[t(_,{type:"primary",secondary:"",onClick:o[28]||(o[28]=a=>J(i(R)))},{icon:s(()=>o[37]||(o[37]=[])),default:s(()=>[t(F),e("span",Qo,l(n.$t("copy")),1)]),_:1})]),_:1})])]),_:1},8,["show"])])}}});export{_n as default};

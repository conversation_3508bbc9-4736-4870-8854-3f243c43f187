import{S as L,U as B,V as G,W as I,d as F,X,r as z,D as M,o as O,Y as Z,Z as S,$,a0 as x,a1 as T,a2 as q,a3 as A,a4 as J,a5 as K,a6 as Q,x as C,s as w,b as y,e as c,a7 as ee,u as te,I as ne,f as n,w as o,J as oe,G as ae,i as v,a8 as se,t as U,l as H,H as le}from"./index-DSmp6iCg.js";import{_ as re,a as ie}from"./send-DLYXsI6z.js";import{_ as ue}from"./chart.vue_vue_type_script_setup_true_lang-EQfFtyu6.js";import{_ as ce}from"./chart2.vue_vue_type_script_setup_true_lang-BlhEQScz.js";import{_ as de}from"./DatePicker-D6AT7i-o.js";import{a as fe,_ as me}from"./Grid-lBi2xqol.js";import"./index-D3Jd_Dd9.js";import"./moment-zH0z38ay.js";import"./Forward-XU0gTivF.js";import"./Input-D61U907N.js";var pe=/\s/;function _e(e){for(var t=e.length;t--&&pe.test(e.charAt(t)););return t}var he=/^\s+/;function ge(e){return e&&e.slice(0,_e(e)+1).replace(he,"")}var E=NaN,ve=/^[-+]0x[0-9a-f]+$/i,xe=/^0b[01]+$/i,be=/^0o[0-7]+$/i,we=parseInt;function P(e){if(typeof e=="number")return e;if(L(e))return E;if(B(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=B(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=ge(e);var l=xe.test(e);return l||be.test(e)?we(e.slice(2),l?2:8):ve.test(e)?E:+e}var ye=1/0,ke=17976931348623157e292;function Se(e){if(!e)return e===0?e:0;if(e=P(e),e===ye||e===-1/0){var t=e<0?-1:1;return t*ke}return e===e?e:0}function $e(e){var t=Se(e),l=t%1;return t===t?l?t-l:t:0}var Te=G.isFinite,ze=Math.min;function Me(e){var t=Math[e];return function(l,s){if(l=P(l),s=s==null?0:ze($e(s),292),s&&Te(l)){var f=(I(l)+"e").split("e"),d=t(f[0]+"e"+(+f[1]+s));return f=(I(d)+"e").split("e"),+(f[0]+"e"+(+f[1]-s))}return t(l)}}var Ce=Me("round");const Ve=e=>1-Math.pow(1-e,5);function Ne(e){const{from:t,to:l,duration:s,onUpdate:f,onFinish:d}=e,u=performance.now(),a=()=>{const i=performance.now(),p=Math.min(i-u,s),h=t+(l-t)*Ve(p/s);if(p===s){d();return}f(h),requestAnimationFrame(a)};a()}const je={to:{type:Number,default:0},precision:{type:Number,default:0},showSeparator:Boolean,locale:String,from:{type:Number,default:0},active:{type:Boolean,default:!0},duration:{type:Number,default:2e3},onFinish:Function},De=F({name:"NumberAnimation",props:je,setup(e){const{localeRef:t}=X("name"),{duration:l}=e,s=z(e.from),f=M(()=>{const{locale:r}=e;return r!==void 0?r:t.value});let d=!1;const u=r=>{s.value=r},a=()=>{var r;s.value=e.to,d=!1,(r=e.onFinish)===null||r===void 0||r.call(e)},i=(r=e.from,g=e.to)=>{d=!0,s.value=e.from,r!==g&&Ne({from:r,to:g,duration:l,onUpdate:u,onFinish:a})},p=M(()=>{var r;const m=Ce(s.value,e.precision).toFixed(e.precision).split("."),_=new Intl.NumberFormat(f.value),b=(r=_.formatToParts(.5).find(j=>j.type==="decimal"))===null||r===void 0?void 0:r.value,V=e.showSeparator?_.format(Number(m[0])):m[0],N=m[1];return{integer:V,decimal:N,decimalSeparator:b}});function h(){d||i()}return O(()=>{Z(()=>{e.active&&i()})}),Object.assign({formattedValue:p},{play:h})},render(){const{formattedValue:{integer:e,decimal:t,decimalSeparator:l}}=this;return[e,t?l:null,t]}}),Fe=S("statistic",[$("label",`
 font-weight: var(--n-label-font-weight);
 transition: .3s color var(--n-bezier);
 font-size: var(--n-label-font-size);
 color: var(--n-label-text-color);
 `),S("statistic-value",`
 margin-top: 4px;
 font-weight: var(--n-value-font-weight);
 `,[$("prefix",`
 margin: 0 4px 0 0;
 font-size: var(--n-value-font-size);
 transition: .3s color var(--n-bezier);
 color: var(--n-value-prefix-text-color);
 `,[S("icon",{verticalAlign:"-0.125em"})]),$("content",`
 font-size: var(--n-value-font-size);
 transition: .3s color var(--n-bezier);
 color: var(--n-value-text-color);
 `),$("suffix",`
 margin: 0 0 0 4px;
 font-size: var(--n-value-font-size);
 transition: .3s color var(--n-bezier);
 color: var(--n-value-suffix-text-color);
 `,[S("icon",{verticalAlign:"-0.125em"})])])]),Re=Object.assign(Object.assign({},A.props),{tabularNums:Boolean,label:String,value:[String,Number]}),Be=F({name:"Statistic",props:Re,slots:Object,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:l,mergedRtlRef:s}=q(e),f=A("Statistic","-statistic",Fe,J,e,t),d=K("Statistic",s,t),u=M(()=>{const{self:{labelFontWeight:i,valueFontSize:p,valueFontWeight:h,valuePrefixTextColor:k,labelTextColor:r,valueSuffixTextColor:g,valueTextColor:m,labelFontSize:_},common:{cubicBezierEaseInOut:b}}=f.value;return{"--n-bezier":b,"--n-label-font-size":_,"--n-label-font-weight":i,"--n-label-text-color":r,"--n-value-font-weight":h,"--n-value-font-size":p,"--n-value-prefix-text-color":k,"--n-value-suffix-text-color":g,"--n-value-text-color":m}}),a=l?Q("statistic",void 0,u,e):void 0;return{rtlEnabled:d,mergedClsPrefix:t,cssVars:l?void 0:u,themeClass:a?.themeClass,onRender:a?.onRender}},render(){var e;const{mergedClsPrefix:t,$slots:{default:l,label:s,prefix:f,suffix:d}}=this;return(e=this.onRender)===null||e===void 0||e.call(this),x("div",{class:[`${t}-statistic`,this.themeClass,this.rtlEnabled&&`${t}-statistic--rtl`],style:this.cssVars},T(s,u=>x("div",{class:`${t}-statistic__label`},this.label||u)),x("div",{class:`${t}-statistic-value`,style:{fontVariantNumeric:this.tabularNums?"tabular-nums":""}},T(f,u=>u&&x("span",{class:`${t}-statistic-value__prefix`},u)),this.value!==void 0?x("span",{class:`${t}-statistic-value__content`},this.value):T(l,u=>u&&x("span",{class:`${t}-statistic-value__content`},u)),T(d,u=>u&&x("span",{class:`${t}-statistic-value__suffix`},u))))}}),Ie={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Ue(e,t){return y(),w("svg",Ie,t[0]||(t[0]=[c("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[c("path",{d:"M31 34h12m-5 5l5-5l-5-5"}),c("path",{d:"M43 26V10a3 3 0 0 0-3-3H8a3 3 0 0 0-3 3v28a3 3 0 0 0 3 3h20.47"}),c("path",{d:"m15 15l5 6l5-6M14 27h12m-12-6h12m-6 0v12"})],-1)]))}const He=C({name:"icon-park-outline-expenses",render:Ue}),Ee={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Oe(e,t){return y(),w("svg",Ee,t[0]||(t[0]=[c("g",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4"},[c("path",{d:"M31 34h12m0-8V10a3 3 0 0 0-3-3H8a3 3 0 0 0-3 3v28a3 3 0 0 0 3 3h20.47"}),c("path",{d:"m36 39l-5-5l5-5M15 15l5 6l5-6M14 27h12m-12-6h12m-6 0v12"})],-1)]))}const Ae=C({name:"icon-park-outline-income",render:Oe}),Pe={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function We(e,t){return y(),w("svg",Pe,t[0]||(t[0]=[ee('<g fill="none" stroke="currentColor" stroke-width="4"><path stroke-linecap="round" stroke-linejoin="round" d="M17.982 11.969L31.785 4l4.612 7.989z" clip-rule="evenodd"></path><path stroke-linejoin="round" d="M4 14a2 2 0 0 1 2-2h36a2 2 0 0 1 2 2v28a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2z"></path><path stroke-linejoin="round" d="M35.25 33H44V23h-8.75c-2.9 0-5.25 2.239-5.25 5s2.35 5 5.25 5Z"></path><path stroke-linecap="round" d="M44 16.5v24"></path></g>',1)]))}const Ye=C({name:"icon-park-outline-wallet",render:We}),Le={style:{display:"inline-block"},viewBox:"0 0 48 48",width:"1.2em",height:"1.2em"};function Ge(e,t){return y(),w("svg",Le,t[0]||(t[0]=[c("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"4",d:"M24 16h5V4l15 15l-15 15V24H18V13L4 28l14 16V32h5"},null,-1)]))}const Xe=C({name:"icon-park-outline-exchange",render:Ge}),Ze={class:"flex flex-wrap sm:flex-nowrap justify-center items-center gap-3"},qe={class:"text-center text-lg font-bold"},Je={class:"flex flex-wrap sm:flex-nowrap justify-center items-center gap-3"},Ke={class:"text-center text-lg font-bold"},ut=F({__name:"index",setup(e){const t=te(),l=M(()=>t.primaryColor),s=z({}),f=async()=>{try{const{data:a}=await le.get("v1/dashboard/getMonitor");s.value=a}catch(a){console.error("Failed to fetch monitor data:",a)}};ne(()=>t.primaryColor,a=>{l.value=a});const d=z(new Date().getTime()),u=z(new Date().getTime());return O(()=>{f()}),(a,i)=>{const p=De,h=Be,k=Xe,r=se,g=ae,m=oe,_=fe,b=Ye,V=re,N=ie,j=Ae,W=He,R=de,Y=me;return y(),w("div",null,[n(Y,{cols:"1 700:2 1024:3 ","x-gap":12,"y-gap":12},{default:o(()=>[n(_,null,{default:o(()=>[n(m,null,{default:o(()=>[n(g,{justify:"space-between",align:"center"},{default:o(()=>[n(h,{label:a.$t("depositTodayTHB")},{suffix:o(()=>i[2]||(i[2]=[c("span",{class:"text-sm text-gray-500"},"฿",-1)])),default:o(()=>[n(p,{from:0,to:v(s).depositTodayTH,"show-separator":""},null,8,["to"])]),_:1},8,["label"]),n(r,{color:"#d03050",size:"42"},{default:o(()=>[n(k)]),_:1})]),_:1})]),_:1})]),_:1}),n(_,null,{default:o(()=>[n(m,null,{default:o(()=>[n(g,{justify:"space-between",align:"center"},{default:o(()=>[n(h,{label:a.$t("exchangeTodayUSDT")},{suffix:o(()=>i[3]||(i[3]=[c("span",{class:"text-sm text-gray-500"},"USDT",-1)])),default:o(()=>[n(p,{from:0,to:v(s).exchangeToday,"show-separator":""},null,8,["to"])]),_:1},8,["label"]),n(r,{color:v(l),size:"42"},{default:o(()=>[n(b)]),_:1},8,["color"])]),_:1})]),_:1})]),_:1}),n(_,null,{default:o(()=>[n(m,null,{default:o(()=>[n(g,{justify:"space-between",align:"center"},{default:o(()=>[n(h,{label:a.$t("withdrawTodayUSDT")},{suffix:o(()=>i[4]||(i[4]=[c("span",{class:"text-sm text-gray-500"},"USDT",-1)])),default:o(()=>[n(p,{from:0,to:v(s).withdrawTodayUSD,"show-separator":""},null,8,["to"])]),_:1},8,["label"]),n(r,{color:"#18a058",size:"42"},{default:o(()=>[n(V)]),_:1})]),_:1})]),_:1})]),_:1}),n(_,null,{default:o(()=>[n(m,null,{default:o(()=>[n(g,{justify:"space-between",align:"center"},{default:o(()=>[n(h,{label:a.$t("depositMonthTHB")},{suffix:o(()=>i[5]||(i[5]=[c("span",{class:"text-sm text-gray-500"},"฿",-1)])),default:o(()=>[n(p,{from:0,to:v(s).depositMonthTH,"show-separator":""},null,8,["to"])]),_:1},8,["label"]),n(r,{color:"#06b6d4",size:"42"},{default:o(()=>[n(N)]),_:1})]),_:1})]),_:1})]),_:1}),n(_,null,{default:o(()=>[n(m,null,{default:o(()=>[n(g,{justify:"space-between",align:"center"},{default:o(()=>[n(h,{label:a.$t("exchangeMonthUSDT")},{suffix:o(()=>i[6]||(i[6]=[c("span",{class:"text-sm text-gray-500"},"USDT",-1)])),default:o(()=>[n(p,{from:0,to:v(s).exchangeMonth,"show-separator":""},null,8,["to"])]),_:1},8,["label"]),n(r,{color:"#f59e0b",size:"42"},{default:o(()=>[n(j)]),_:1})]),_:1})]),_:1})]),_:1}),n(_,null,{default:o(()=>[n(m,null,{default:o(()=>[n(g,{justify:"space-between",align:"center"},{default:o(()=>[n(h,{label:a.$t("withdrawMonthUSDT")},{suffix:o(()=>i[7]||(i[7]=[c("span",{class:"text-sm text-gray-500"},"USDT",-1)])),default:o(()=>[n(p,{from:0,to:v(s).withdrawMonthUSD,"show-separator":""},null,8,["to"])]),_:1},8,["label"]),n(r,{color:"#8b5cf6",size:"42"},{default:o(()=>[n(W)]),_:1})]),_:1})]),_:1})]),_:1}),n(_,{span:"8"},{default:o(()=>[n(m,null,{default:o(()=>[c("div",Ze,[c("p",qe,U(a.$t("dashboard.annualDepositWithdrawal")),1),n(R,{type:"year",placeholder:a.$t("dashboard.selectYear"),value:v(u),"onUpdate:value":i[0]||(i[0]=D=>H(u)?u.value=D:null),class:"w-28 z-10"},null,8,["placeholder","value"])]),n(ue,{year:v(u)},null,8,["year"])]),_:1})]),_:1}),n(_,{span:"8"},{default:o(()=>[n(m,null,{default:o(()=>[c("div",Je,[c("p",Ke,U(a.$t("dashboard.thbUsdtExchangeVolume")),1),n(R,{type:"year",placeholder:a.$t("dashboard.selectYear"),value:v(d),"onUpdate:value":i[1]||(i[1]=D=>H(d)?d.value=D:null),class:"w-28 z-10"},null,8,["placeholder","value"])]),n(ce,{year:v(d)},null,8,["year"])]),_:1})]),_:1})]),_:1})])}}});export{ut as default};
